import expressWs from "express-ws"
import { <PERSON><PERSON><PERSON>, EmptyObject, Page } from "../types.js"
import { BypassError, deductCredits } from "./index.js"
import { getCallerName, obfuscate, sleep } from "../utils.js"
import Websocket from "ws"
import { RequestInterceptionManager } from "puppeteer-intercept-and-modify-requests"
import path from "path"
import { readFileSync, writeFileSync } from "fs"
import { _dirname } from "../index.js"
import { config } from "../config.js"

/* 
    Every client packet type

    c_workink_pass_available
    c_focus_lost
    c_adblocker_detected
    c_focus
    c_announce
    c_offers_skipped
    c_monetization
    c_ping
    c_social_started
    c_recaptcha_response
    c_hcaptcha_response
    c_turnstile_response
    c_workink_pass_use
 */
/* 
    Every server packet type

    s_social_done
    s_social_running
    s_monetization
    s_start_recaptcha_check
    s_start_turnstile_check
    s_link_destination
    s_link_not_found
    s_redirection_canceled
    s_premium_only
    s_error
    s_recaptcha_okay
    s_link_info
    s_pong
    s_hcaptcha_okay
    s_proxy_detected
    s_workink_pass_left
    s_start_hcaptcha_check
 */

type Packet =
	// Client packets
	| { type: "c_social_started"; payload: { url: string } }
	| {
			type: "c_recaptcha_response"
			payload: {
				recaptchaResponse: string
			}
	  }
	| {
			type: "c_turnstile_response"
			payload: {
				token: string
			}
	  }
	| { type: "c_workink_pass_use"; payload: EmptyObject }
	| { type: "c_monetization"; payload: { type: string; payload: { event: string } } }
	| { type: "c_ping"; payload: { timestamp: number } }
	// Server packets
	| { type: "s_pong"; payload: { timestamp: number } }
	| {
			type: "s_link_info"
			payload: {
				id: number
				isAdblockEnabled: boolean
				isDesktopOnly: boolean
				monetizationLevel: string
				monetizations: number[]
				monetizationsNeeded: number
				onlyStaticMonetizations: boolean
				socials: { type: string; url: string }[]
				userId: number
			}
	  }
	| { type: "s_social_done"; payload: { url: string } }
	| { type: "s_social_running"; payload: { time: number; url: string } }
	| {
			type: "s_link_destination"
			payload: {
				url: string
				usingPass: boolean
				usingPremium: boolean
			}
	  }
	| { type: "s_start_recaptcha_check"; payload: { siteKey: string } }

// cSpell:disable
const monetizationMap = {
	20: "POPUPS_DESKTOP",
	21: "POPUPS_MOBILE",
	22: "READ_ARTICLES",
	23: "INSTALLER",
	24: "OPERA",
	25: "OPERA_GX",
	27: "BUFF_DESKTOP",
	28: "BUFF_MOBILE",
	29: "BROWSER_EXTENSION",
	32: "NORDVPN",
	33: "RAID_SHADOW_LEGENDS",
	34: "NORTON_ANTIVIRUS",
	36: "LUME_BROWSER_ANDROID",
	37: "PUSH_NOTIFICATIONS",
	38: "PUSH_NOTIFICATIONS_FULLSCREEN",
	39: "TIKTOK",
	40: "INSTALL_APP",
	41: "OPERAGX_MOBILE",
	42: "QUICKSNAP",
	43: "TEMU",
	44: "SHEIN",
	45: "PDF_EDITOR",
	47: "OPERA_GX_IOS",
	48: "W_AUTO_CLICKER",
	49: "W_FLASHLIGHT",
	50: "W_PRANK_CALL_VIDEO",
	51: "W_ANTI_THEFT_MY_PHONE",
	52: "W_FLASHLIGHT_SOS",
	53: "W_AMAZON_SHOPPING",
	54: "W_CASH_EM_ALL",
	55: "W_MOBILE_LEGENDS",
	56: "W_CASH_GIRAFFE",
	57: "BETTERDEALS",
	58: "OPERA_POPUP",
	60: "LDPLAYER",
	61: "PIPPIT",
	62: "ONTHATASS",
	63: "PRIMEVIDEO",
	64: "RIVAGAUCHE",
	65: "LENME",
	66: "TESTERUP",
	67: "XANH",
	68: "TIMO",
	69: "SOLITAIRECASH",
	70: "GAUTHAI",
	71: "EXTERNAL_ARTICLES",
	POPUPS_DESKTOP: 20,
	POPUPS_MOBILE: 21,
	READ_ARTICLES: 22,
	INSTALLER: 23,
	OPERA: 24,
	OPERA_GX: 25,
	BUFF_DESKTOP: 27,
	BUFF_MOBILE: 28,
	BROWSER_EXTENSION: 29,
	NORDVPN: 32,
	RAID_SHADOW_LEGENDS: 33,
	NORTON_ANTIVIRUS: 34,
	LUME_BROWSER_ANDROID: 36,
	PUSH_NOTIFICATIONS: 37,
	PUSH_NOTIFICATIONS_FULLSCREEN: 38,
	TIKTOK: 39,
	INSTALL_APP: 40,
	OPERAGX_MOBILE: 41,
	QUICKSNAP: 42,
	TEMU: 43,
	SHEIN: 44,
	PDF_EDITOR: 45,
	OPERA_GX_IOS: 47,
	W_AUTO_CLICKER: 48,
	W_FLASHLIGHT: 49,
	W_PRANK_CALL_VIDEO: 50,
	W_ANTI_THEFT_MY_PHONE: 51,
	W_FLASHLIGHT_SOS: 52,
	W_AMAZON_SHOPPING: 53,
	W_CASH_EM_ALL: 54,
	W_MOBILE_LEGENDS: 55,
	W_CASH_GIRAFFE: 56,
	BETTERDEALS: 57,
	OPERA_POPUP: 58,
	LDPLAYER: 60,
	PIPPIT: 61,
	ONTHATASS: 62,
	PRIMEVIDEO: 63,
	RIVAGAUCHE: 64,
	LENME: 65,
	TESTERUP: 66,
	XANH: 67,
	TIMO: 68,
	SOLITAIRECASH: 69,
	GAUTHAI: 70,
	EXTERNAL_ARTICLES: 71,
} as const

const monetizationPayloads: Partial<
	Record<keyof typeof monetizationMap, { type: string; payload: { event: string } }>
> = {
	PDF_EDITOR: { type: "pdfeditor", payload: { event: "installed" } },
	READ_ARTICLES: { type: "readArticles2", payload: { event: "read" } },
} as const
// cSpell:enable

const encryptionTokenUpdateInterval = 1000 * 60 * 60
let encryptionTokenOne = "DANFG1xMHYWMkT71OdhA8NWi1maeHEcJeERWxXZBvKZL1JqmFYPUg7TgY2utR61H"
let encryptionTokenTwo = "3Tm0c3oGvS4ElZydg7YqctFZoQDUBicV4aYqIAi93Gm9IJwVQ86F7JgKfi1m7zlW"

async function updateEncryptionTokens(browser: Browser) {
	let page: Page
	try {
		console.log("Getting encryption tokens")

		page = await browser.newPage()

		const client = await page.createCDPSession()
		const interceptManager = new RequestInterceptionManager(client)
		await interceptManager.intercept({
			urlPattern: `https://work.ink/_app/immutable/nodes/3.*`,
			modifyResponse({ body }) {
				if (!body) return

				// Save original script to a file for debugging
				writeFileSync(path.join(_dirname, "../workInkReversing/original.js"), body)

				// Get the name of the websocket wrapper class
				const className = body
					.match(/class \w+(?=.+?wss:\/\/)/g)
					?.pop()
					?.slice(6)

				if (!className) console.error("Failed to find class name")

				// Inject code to get encryption tokens
				body = body.replace(
					new RegExp(
						`class ${className}{constructor\\([a-zA-Z0-9_$]+,[a-zA-Z0-9_$]+\\){(?:[a-zA-Z0-9_$]+\\(this,.+?){26},async [a-zA-Z0-9_$]+=>{.+?let [a-zA-Z0-9_$]+,([a-zA-Z0-9_$]+)=.+?,([a-zA-Z0-9_$]+)=.+?;`
					),
					(substring, ...args) => {
						const varName1 = args[0]
						const varName2 = args[1]

						console.log("Found encryption token variables", varName1, varName2)

						return (
							substring +
							`window.encryptionTokenOne=${varName1};window.encryptionTokenTwo=${varName2};`
						)
					}
				)

				// Save modified script to a file for debugging
				writeFileSync(path.join(_dirname, "../workInkReversing/modified.js"), body)

				return { body }
			},
		})

		await page.goto("https://work.ink/1ZRk/ltrgbw20")

		while (true) {
			console.log("Waiting for cloudflare protection")
			await sleep(1000)
			if (!(await page.$(".challenge-wrapper"))) break
		}

        await sleep(2000)

		console.log("Cloudflare protection passed")

		const encryptionTokensHandle = await page.waitForFunction(() => {
			//@ts-expect-error
			if (window.encryptionTokenOne && window.encryptionTokenTwo) {
				//@ts-expect-error
				console.log(window.encryptionTokenOne, window.encryptionTokenTwo) //@ts-expect-error
				return [window.encryptionTokenOne, window.encryptionTokenTwo]
			}
			return false
        })

		const encryptionTokens = (await encryptionTokensHandle.jsonValue()) as [string, string]

		console.log("Encryption tokens found", encryptionTokens)

		encryptionTokenOne = encryptionTokens[0]
		encryptionTokenTwo = encryptionTokens[1]
	} catch (error) {
		console.error("Error getting encryption tokens", error)
	} finally {
		// Temporary delay to try to see why it sometimes fails
		setTimeout(() => page?.close(), 1000 * 60)
	}
}

function decryptPacket(packetData: string): Packet {
	try {
		let packet
		let l = ""
		const a = encryptionTokenOne.split("").map(p => p.charCodeAt(0)),
			r = packetData.substring(0, 2)
		let d = parseInt(r, 16)
		const o = packetData.substring(2).match(/.{1,2}/g) || []
		for (let p = 0; p < o.length; p++) {
			const m = parseInt(o[p], 16),
				L = a[(p * 2 + d) % a.length],
				j = (m - (p % 8) + 256) % 256,
				R = String.fromCharCode(j ^ L)
			;(l += R), (d = (d * 19 + 29) % 256)
		}
		packet = JSON.parse(l)
		let f = ""
		const b = encryptionTokenTwo.split("").map(p => p.charCodeAt(0)),
			k = packet.payload.substring(0, 2)
		let g = parseInt(k, 16)
		const h = packet.payload.substring(2).match(/.{1,2}/g) || []
		for (let p = 0; p < h.length; p++) {
			const m = parseInt(h[p], 16),
				L = b[(p * 2 + g) % b.length],
				j = (m - (p % 8) + 256) % 256,
				R = String.fromCharCode(j ^ L)
			;(f += R), (g = (g * 19 + 29) % 256)
		}
		packet.payload = JSON.parse(f)
		return packet
	} catch (error) {
		throw new BypassError("Error 1")
	}
}

function encryptPacket(packet: Packet): string {
	try {
		function encrypt(data: string, token: string) {
			let g = ""
			const D = token.split("").map(m => m.charCodeAt(0))
			let M = Date.now() % 256 // Doesn't need to be actual date it can be anything
			g += M.toString(16).padStart(2, "0")
			for (let m = 0; m < data.length; m++) {
				const b = data.charCodeAt(m),
					C = D[(m * 2 + M) % D.length],
					P = ((b ^ C) + (m % 8)) % 256
				;(g += P.toString(16).padStart(2, "0")), (M = (M * 19 + 29) % 256)
			}
			return g
		}

		const encryptedPayload = encrypt(JSON.stringify(packet.payload), encryptionTokenTwo)

		const packetString = JSON.stringify({
			type: packet.type,
			payload: encryptedPayload,
		})

		const encryptedPacket = encrypt(packetString, encryptionTokenOne)

		return encryptedPacket
	} catch (error) {
		throw new BypassError("Error 2")
	}
}

function handleError(ws: Websocket, error: unknown) {
	if (error instanceof BypassError) {
		ws.send("Error: " + error.message)
	} else {
		console.error(error)
	}
	ws.close()
}

export async function workInkBypass(app: expressWs.Application, browser: Browser) {
	await updateEncryptionTokens(browser)

	setInterval(async () => {
		await updateEncryptionTokens(browser)
	}, encryptionTokenUpdateInterval)

	const loader = readFileSync(path.join(_dirname, "bypasses/workInk.user.js"), "utf8").replace(
		"{{DOMAIN}}",
		config.domain
	)
	app.get("/workInkLoader", async (req, res) => {
		res.send(loader)
	})

	const obfuscatedScript = obfuscate(
		readFileSync(path.join(_dirname, "bypasses/workInk.client.js"), "utf8").replace(
			"{{DOMAIN}}",
			config.domain
		)
	)
	app.get("/workInk", async (req, res) => {
		res.send((await obfuscatedScript.next()).value)
	})

	app.ws("/workInk", async (ws, req) => {
		try {
			await deductCredits(req, 1, "workInk bypass")

			let linkInfo: Extract<Packet, { type: "s_link_info" }>

			ws.on("message", async message => {
				try {
					const data = message.toString()
					const packet = decryptPacket(data)

					// Ignore pings and pongs
					if (packet.type === "c_ping" || packet.type === "s_pong") {
						return
					}

					console.log("Receiving packet", packet)

					const packetQueue: { packet: Packet; delay: number }[] = [{ packet, delay: 0 }]

					switch (packet.type) {
						case "c_turnstile_response": {
							if (!linkInfo) throw new Error("Link info not found")

							if (
								linkInfo.payload.monetizationsNeeded !== linkInfo.payload.monetizations.length
							) {
								console.warn(
									"Monetizations needed",
									linkInfo.payload.monetizationsNeeded,
									"but got",
									linkInfo.payload.monetizations.length
								)
							}

							let i = 0

							// Complete monetizations
							for (const monetizationId of linkInfo.payload.monetizations) {
								if (!(monetizationId in monetizationMap))
									throw new Error(`Monetization missing in map ${monetizationId}`)

								const monetizationName =
									monetizationMap[monetizationId as keyof typeof monetizationMap]

								const payloadData =
									monetizationPayloads[monetizationName as keyof typeof monetizationPayloads]

								if (!payloadData)
									throw new Error(
										`Unhandled monetization ${monetizationId} ${monetizationName}`
									)

								packetQueue.push({
									packet: {
										type: "c_monetization",
										payload: payloadData,
									},
									delay: 100 * ++i,
								})
							}

							// Complete socials
							for (const social of linkInfo.payload.socials) {
								packetQueue.push({
									packet: { type: "c_social_started", payload: { url: social.url } },
									delay: 100 * ++i,
								})
							}

							break
						}
						case "s_link_destination": {
							packetQueue.pop()

							let modifiedPacket = packet
							modifiedPacket.payload.usingPass = false

							packetQueue.push({
								packet: modifiedPacket,
								delay: 0,
							})

							ws.send("url" + packet.payload.url)
							break
						}

						// Disable recaptcha check
						case "c_recaptcha_response":
						case "s_start_recaptcha_check": {
							packetQueue.pop()
							break
						}
					}

					for (const { packet, delay } of packetQueue) {
						console.log("Sending packet", packet, "with delay", delay)
						await sleep(delay)
						if (packet.type.startsWith("c")) {
							ws.send("toServer" + encryptPacket(packet))
						} else if (packet.type.startsWith("s")) {
							ws.send("toClient" + encryptPacket(packet))
						} else {
							throw new Error("Invalid packet type")
						}
					}
				} catch (error) {
					handleError(ws, error)
				}
			})
		} catch (error) {
			handleError(ws, error)
		}
	})
}
