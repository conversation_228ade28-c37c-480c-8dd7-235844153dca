;(() => {
	"use strict"
	var e = {
			136: function (e, t, n) {
				var r =
					(this && this.__awaiter) ||
					function (e, t, n, r) {
						return new (n || (n = Promise))(function (o, u) {
							function a(e) {
								try {
									i(r.next(e))
								} catch (e) {
									u(e)
								}
							}
							function l(e) {
								try {
									i(r.throw(e))
								} catch (e) {
									u(e)
								}
							}
							function i(e) {
								var t
								e.done
									? o(e.value)
									: ((t = e.value),
									  t instanceof n
											? t
											: new n(function (e) {
													e(t)
											  })).then(a, l)
							}
							i((r = r.apply(e, t || [])).next())
						})
					}
				Object.defineProperty(t, "__esModule", { value: !0 })
				const o = n(429)
				function u(e, t) {
					chrome.management.get(e, e => {
						chrome.runtime.lastError ? t(!1) : t(!0)
					})
				}
				r(void 0, void 0, void 0, function* () {
					;(yield chrome.storage.local.get(["fingerprint"])).fingerprint ||
						(yield chrome.storage.local.set({ fingerprint: (0, o.v4)() + "-" + Date.now() })),
						chrome.storage.session.setAccessLevel({
							accessLevel: "TRUSTED_AND_UNTRUSTED_CONTEXTS",
						})
					let e = !1
					const t = () => {
						e || chrome.storage.local.remove("adblockerInstalled")
					}
					setInterval(() => {
						;(e = !1),
							u("cjpalhdlnbpafiamejdnhcphjbkeiagm", n => {
								n &&
									(chrome.storage.local.set({ adblockerInstalled: "uBlock Origin" }),
									(e = !0)),
									t()
							}),
							u("cfhdojbkjhnklbpkdaibdccddilifddb", n => {
								n &&
									(chrome.storage.local.set({ adblockerInstalled: "AdBlockPlus" }),
									(e = !0)),
									t()
							}),
							u("bgnkhhnnamicmpeenaelnjfhikgbkllg", n => {
								n && (chrome.storage.local.set({ adblockerInstalled: "AdGuard" }), (e = !0)),
									t()
							})
					}, 5e3)
				}),
					chrome.tabs.query({ active: !0, currentWindow: !0 }, e => {
						chrome.runtime.onMessageExternal.addListener((e, t, n) =>
							r(void 0, void 0, void 0, function* () {
								const t = yield chrome.storage.local.get(["fingerprint"])
								let r = ""
								t.fingerprint && (r = t.fingerprint),
									e &&
										e.message &&
										"wk_installed" == e.message &&
										n({ installed: !0, name: "workink-pass", fingerprint: r })
							})
						)
					})
			},
			429: (e, t, n) => {
				Object.defineProperty(t, "__esModule", { value: !0 }),
					Object.defineProperty(t, "NIL", {
						enumerable: !0,
						get: function () {
							return l.default
						},
					}),
					Object.defineProperty(t, "parse", {
						enumerable: !0,
						get: function () {
							return c.default
						},
					}),
					Object.defineProperty(t, "stringify", {
						enumerable: !0,
						get: function () {
							return f.default
						},
					}),
					Object.defineProperty(t, "v1", {
						enumerable: !0,
						get: function () {
							return r.default
						},
					}),
					Object.defineProperty(t, "v3", {
						enumerable: !0,
						get: function () {
							return o.default
						},
					}),
					Object.defineProperty(t, "v4", {
						enumerable: !0,
						get: function () {
							return u.default
						},
					}),
					Object.defineProperty(t, "v5", {
						enumerable: !0,
						get: function () {
							return a.default
						},
					}),
					Object.defineProperty(t, "validate", {
						enumerable: !0,
						get: function () {
							return d.default
						},
					}),
					Object.defineProperty(t, "version", {
						enumerable: !0,
						get: function () {
							return i.default
						},
					})
				var r = s(n(990)),
					o = s(n(237)),
					u = s(n(355)),
					a = s(n(764)),
					l = s(n(314)),
					i = s(n(464)),
					d = s(n(435)),
					f = s(n(8)),
					c = s(n(222))
				function s(e) {
					return e && e.__esModule ? e : { default: e }
				}
			},
			163: (e, t) => {
				function n(e) {
					return 14 + (((e + 64) >>> 9) << 4) + 1
				}
				function r(e, t) {
					const n = (65535 & e) + (65535 & t)
					return (((e >> 16) + (t >> 16) + (n >> 16)) << 16) | (65535 & n)
				}
				function o(e, t, n, o, u, a) {
					return r(((l = r(r(t, e), r(o, a))) << (i = u)) | (l >>> (32 - i)), n)
					var l, i
				}
				function u(e, t, n, r, u, a, l) {
					return o((t & n) | (~t & r), e, t, u, a, l)
				}
				function a(e, t, n, r, u, a, l) {
					return o((t & r) | (n & ~r), e, t, u, a, l)
				}
				function l(e, t, n, r, u, a, l) {
					return o(t ^ n ^ r, e, t, u, a, l)
				}
				function i(e, t, n, r, u, a, l) {
					return o(n ^ (t | ~r), e, t, u, a, l)
				}
				Object.defineProperty(t, "__esModule", { value: !0 }), (t.default = void 0)
				t.default = function (e) {
					if ("string" == typeof e) {
						const t = unescape(encodeURIComponent(e))
						e = new Uint8Array(t.length)
						for (let n = 0; n < t.length; ++n) e[n] = t.charCodeAt(n)
					}
					return (function (e) {
						const t = [],
							n = 32 * e.length,
							r = "0123456789abcdef"
						for (let o = 0; o < n; o += 8) {
							const n = (e[o >> 5] >>> o % 32) & 255,
								u = parseInt(r.charAt((n >>> 4) & 15) + r.charAt(15 & n), 16)
							t.push(u)
						}
						return t
					})(
						(function (e, t) {
							;(e[t >> 5] |= 128 << t % 32), (e[n(t) - 1] = t)
							let o = 1732584193,
								d = -271733879,
								f = -1732584194,
								c = 271733878
							for (let t = 0; t < e.length; t += 16) {
								const n = o,
									s = d,
									p = f,
									v = c
								;(o = u(o, d, f, c, e[t], 7, -680876936)),
									(c = u(c, o, d, f, e[t + 1], 12, -389564586)),
									(f = u(f, c, o, d, e[t + 2], 17, 606105819)),
									(d = u(d, f, c, o, e[t + 3], 22, -1044525330)),
									(o = u(o, d, f, c, e[t + 4], 7, -176418897)),
									(c = u(c, o, d, f, e[t + 5], 12, 1200080426)),
									(f = u(f, c, o, d, e[t + 6], 17, -1473231341)),
									(d = u(d, f, c, o, e[t + 7], 22, -45705983)),
									(o = u(o, d, f, c, e[t + 8], 7, 1770035416)),
									(c = u(c, o, d, f, e[t + 9], 12, -1958414417)),
									(f = u(f, c, o, d, e[t + 10], 17, -42063)),
									(d = u(d, f, c, o, e[t + 11], 22, -1990404162)),
									(o = u(o, d, f, c, e[t + 12], 7, 1804603682)),
									(c = u(c, o, d, f, e[t + 13], 12, -40341101)),
									(f = u(f, c, o, d, e[t + 14], 17, -1502002290)),
									(d = u(d, f, c, o, e[t + 15], 22, 1236535329)),
									(o = a(o, d, f, c, e[t + 1], 5, -165796510)),
									(c = a(c, o, d, f, e[t + 6], 9, -1069501632)),
									(f = a(f, c, o, d, e[t + 11], 14, 643717713)),
									(d = a(d, f, c, o, e[t], 20, -373897302)),
									(o = a(o, d, f, c, e[t + 5], 5, -701558691)),
									(c = a(c, o, d, f, e[t + 10], 9, 38016083)),
									(f = a(f, c, o, d, e[t + 15], 14, -660478335)),
									(d = a(d, f, c, o, e[t + 4], 20, -405537848)),
									(o = a(o, d, f, c, e[t + 9], 5, 568446438)),
									(c = a(c, o, d, f, e[t + 14], 9, -1019803690)),
									(f = a(f, c, o, d, e[t + 3], 14, -187363961)),
									(d = a(d, f, c, o, e[t + 8], 20, 1163531501)),
									(o = a(o, d, f, c, e[t + 13], 5, -1444681467)),
									(c = a(c, o, d, f, e[t + 2], 9, -51403784)),
									(f = a(f, c, o, d, e[t + 7], 14, 1735328473)),
									(d = a(d, f, c, o, e[t + 12], 20, -1926607734)),
									(o = l(o, d, f, c, e[t + 5], 4, -378558)),
									(c = l(c, o, d, f, e[t + 8], 11, -2022574463)),
									(f = l(f, c, o, d, e[t + 11], 16, 1839030562)),
									(d = l(d, f, c, o, e[t + 14], 23, -35309556)),
									(o = l(o, d, f, c, e[t + 1], 4, -1530992060)),
									(c = l(c, o, d, f, e[t + 4], 11, 1272893353)),
									(f = l(f, c, o, d, e[t + 7], 16, -155497632)),
									(d = l(d, f, c, o, e[t + 10], 23, -1094730640)),
									(o = l(o, d, f, c, e[t + 13], 4, 681279174)),
									(c = l(c, o, d, f, e[t], 11, -358537222)),
									(f = l(f, c, o, d, e[t + 3], 16, -722521979)),
									(d = l(d, f, c, o, e[t + 6], 23, 76029189)),
									(o = l(o, d, f, c, e[t + 9], 4, -640364487)),
									(c = l(c, o, d, f, e[t + 12], 11, -421815835)),
									(f = l(f, c, o, d, e[t + 15], 16, 530742520)),
									(d = l(d, f, c, o, e[t + 2], 23, -995338651)),
									(o = i(o, d, f, c, e[t], 6, -198630844)),
									(c = i(c, o, d, f, e[t + 7], 10, 1126891415)),
									(f = i(f, c, o, d, e[t + 14], 15, -1416354905)),
									(d = i(d, f, c, o, e[t + 5], 21, -57434055)),
									(o = i(o, d, f, c, e[t + 12], 6, 1700485571)),
									(c = i(c, o, d, f, e[t + 3], 10, -1894986606)),
									(f = i(f, c, o, d, e[t + 10], 15, -1051523)),
									(d = i(d, f, c, o, e[t + 1], 21, -2054922799)),
									(o = i(o, d, f, c, e[t + 8], 6, 1873313359)),
									(c = i(c, o, d, f, e[t + 15], 10, -30611744)),
									(f = i(f, c, o, d, e[t + 6], 15, -1560198380)),
									(d = i(d, f, c, o, e[t + 13], 21, 1309151649)),
									(o = i(o, d, f, c, e[t + 4], 6, -145523070)),
									(c = i(c, o, d, f, e[t + 11], 10, -1120210379)),
									(f = i(f, c, o, d, e[t + 2], 15, 718787259)),
									(d = i(d, f, c, o, e[t + 9], 21, -343485551)),
									(o = r(o, n)),
									(d = r(d, s)),
									(f = r(f, p)),
									(c = r(c, v))
							}
							return [o, d, f, c]
						})(
							(function (e) {
								if (0 === e.length) return []
								const t = 8 * e.length,
									r = new Uint32Array(n(t))
								for (let n = 0; n < t; n += 8) r[n >> 5] |= (255 & e[n / 8]) << n % 32
								return r
							})(e),
							8 * e.length
						)
					)
				}
			},
			790: (e, t) => {
				Object.defineProperty(t, "__esModule", { value: !0 }), (t.default = void 0)
				var n = {
					randomUUID:
						"undefined" != typeof crypto && crypto.randomUUID && crypto.randomUUID.bind(crypto),
				}
				t.default = n
			},
			314: (e, t) => {
				Object.defineProperty(t, "__esModule", { value: !0 }),
					(t.default = void 0),
					(t.default = "00000000-0000-0000-0000-000000000000")
			},
			222: (e, t, n) => {
				Object.defineProperty(t, "__esModule", { value: !0 }), (t.default = void 0)
				var r,
					o = (r = n(435)) && r.__esModule ? r : { default: r }
				t.default = function (e) {
					if (!(0, o.default)(e)) throw TypeError("Invalid UUID")
					let t
					const n = new Uint8Array(16)
					return (
						(n[0] = (t = parseInt(e.slice(0, 8), 16)) >>> 24),
						(n[1] = (t >>> 16) & 255),
						(n[2] = (t >>> 8) & 255),
						(n[3] = 255 & t),
						(n[4] = (t = parseInt(e.slice(9, 13), 16)) >>> 8),
						(n[5] = 255 & t),
						(n[6] = (t = parseInt(e.slice(14, 18), 16)) >>> 8),
						(n[7] = 255 & t),
						(n[8] = (t = parseInt(e.slice(19, 23), 16)) >>> 8),
						(n[9] = 255 & t),
						(n[10] = ((t = parseInt(e.slice(24, 36), 16)) / 1099511627776) & 255),
						(n[11] = (t / 4294967296) & 255),
						(n[12] = (t >>> 24) & 255),
						(n[13] = (t >>> 16) & 255),
						(n[14] = (t >>> 8) & 255),
						(n[15] = 255 & t),
						n
					)
				}
			},
			58: (e, t) => {
				Object.defineProperty(t, "__esModule", { value: !0 }),
					(t.default = void 0),
					(t.default =
						/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i)
			},
			319: (e, t) => {
				let n
				Object.defineProperty(t, "__esModule", { value: !0 }),
					(t.default = function () {
						if (
							!n &&
							((n =
								"undefined" != typeof crypto &&
								crypto.getRandomValues &&
								crypto.getRandomValues.bind(crypto)),
							!n)
						)
							throw new Error(
								"crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported"
							)
						return n(r)
					})
				const r = new Uint8Array(16)
			},
			757: (e, t) => {
				function n(e, t, n, r) {
					switch (e) {
						case 0:
							return (t & n) ^ (~t & r)
						case 1:
						case 3:
							return t ^ n ^ r
						case 2:
							return (t & n) ^ (t & r) ^ (n & r)
					}
				}
				function r(e, t) {
					return (e << t) | (e >>> (32 - t))
				}
				Object.defineProperty(t, "__esModule", { value: !0 }), (t.default = void 0)
				t.default = function (e) {
					const t = [1518500249, 1859775393, 2400959708, 3395469782],
						o = [1732584193, 4023233417, 2562383102, 271733878, 3285377520]
					if ("string" == typeof e) {
						const t = unescape(encodeURIComponent(e))
						e = []
						for (let n = 0; n < t.length; ++n) e.push(t.charCodeAt(n))
					} else Array.isArray(e) || (e = Array.prototype.slice.call(e))
					e.push(128)
					const u = e.length / 4 + 2,
						a = Math.ceil(u / 16),
						l = new Array(a)
					for (let t = 0; t < a; ++t) {
						const n = new Uint32Array(16)
						for (let r = 0; r < 16; ++r)
							n[r] =
								(e[64 * t + 4 * r] << 24) |
								(e[64 * t + 4 * r + 1] << 16) |
								(e[64 * t + 4 * r + 2] << 8) |
								e[64 * t + 4 * r + 3]
						l[t] = n
					}
					;(l[a - 1][14] = (8 * (e.length - 1)) / Math.pow(2, 32)),
						(l[a - 1][14] = Math.floor(l[a - 1][14])),
						(l[a - 1][15] = (8 * (e.length - 1)) & 4294967295)
					for (let e = 0; e < a; ++e) {
						const u = new Uint32Array(80)
						for (let t = 0; t < 16; ++t) u[t] = l[e][t]
						for (let e = 16; e < 80; ++e) u[e] = r(u[e - 3] ^ u[e - 8] ^ u[e - 14] ^ u[e - 16], 1)
						let a = o[0],
							i = o[1],
							d = o[2],
							f = o[3],
							c = o[4]
						for (let e = 0; e < 80; ++e) {
							const o = Math.floor(e / 20),
								l = (r(a, 5) + n(o, i, d, f) + c + t[o] + u[e]) >>> 0
							;(c = f), (f = d), (d = r(i, 30) >>> 0), (i = a), (a = l)
						}
						;(o[0] = (o[0] + a) >>> 0),
							(o[1] = (o[1] + i) >>> 0),
							(o[2] = (o[2] + d) >>> 0),
							(o[3] = (o[3] + f) >>> 0),
							(o[4] = (o[4] + c) >>> 0)
					}
					return [
						(o[0] >> 24) & 255,
						(o[0] >> 16) & 255,
						(o[0] >> 8) & 255,
						255 & o[0],
						(o[1] >> 24) & 255,
						(o[1] >> 16) & 255,
						(o[1] >> 8) & 255,
						255 & o[1],
						(o[2] >> 24) & 255,
						(o[2] >> 16) & 255,
						(o[2] >> 8) & 255,
						255 & o[2],
						(o[3] >> 24) & 255,
						(o[3] >> 16) & 255,
						(o[3] >> 8) & 255,
						255 & o[3],
						(o[4] >> 24) & 255,
						(o[4] >> 16) & 255,
						(o[4] >> 8) & 255,
						255 & o[4],
					]
				}
			},
			8: (e, t, n) => {
				Object.defineProperty(t, "__esModule", { value: !0 }),
					(t.default = void 0),
					(t.unsafeStringify = a)
				var r,
					o = (r = n(435)) && r.__esModule ? r : { default: r }
				const u = []
				for (let e = 0; e < 256; ++e) u.push((e + 256).toString(16).slice(1))
				function a(e, t = 0) {
					return (
						u[e[t + 0]] +
						u[e[t + 1]] +
						u[e[t + 2]] +
						u[e[t + 3]] +
						"-" +
						u[e[t + 4]] +
						u[e[t + 5]] +
						"-" +
						u[e[t + 6]] +
						u[e[t + 7]] +
						"-" +
						u[e[t + 8]] +
						u[e[t + 9]] +
						"-" +
						u[e[t + 10]] +
						u[e[t + 11]] +
						u[e[t + 12]] +
						u[e[t + 13]] +
						u[e[t + 14]] +
						u[e[t + 15]]
					)
				}
				t.default = function (e, t = 0) {
					const n = a(e, t)
					if (!(0, o.default)(n)) throw TypeError("Stringified UUID is invalid")
					return n
				}
			},
			990: (e, t, n) => {
				Object.defineProperty(t, "__esModule", { value: !0 }), (t.default = void 0)
				var r,
					o = (r = n(319)) && r.__esModule ? r : { default: r },
					u = n(8)
				let a,
					l,
					i = 0,
					d = 0
				t.default = function (e, t, n) {
					let r = (t && n) || 0
					const f = t || new Array(16)
					let c = (e = e || {}).node || a,
						s = void 0 !== e.clockseq ? e.clockseq : l
					if (null == c || null == s) {
						const t = e.random || (e.rng || o.default)()
						null == c && (c = a = [1 | t[0], t[1], t[2], t[3], t[4], t[5]]),
							null == s && (s = l = 16383 & ((t[6] << 8) | t[7]))
					}
					let p = void 0 !== e.msecs ? e.msecs : Date.now(),
						v = void 0 !== e.nsecs ? e.nsecs : d + 1
					const y = p - i + (v - d) / 1e4
					if (
						(y < 0 && void 0 === e.clockseq && (s = (s + 1) & 16383),
						(y < 0 || p > i) && void 0 === e.nsecs && (v = 0),
						v >= 1e4)
					)
						throw new Error("uuid.v1(): Can't create more than 10M uuids/sec")
					;(i = p), (d = v), (l = s), (p += 122192928e5)
					const g = (1e4 * (268435455 & p) + v) % 4294967296
					;(f[r++] = (g >>> 24) & 255),
						(f[r++] = (g >>> 16) & 255),
						(f[r++] = (g >>> 8) & 255),
						(f[r++] = 255 & g)
					const h = ((p / 4294967296) * 1e4) & 268435455
					;(f[r++] = (h >>> 8) & 255),
						(f[r++] = 255 & h),
						(f[r++] = ((h >>> 24) & 15) | 16),
						(f[r++] = (h >>> 16) & 255),
						(f[r++] = (s >>> 8) | 128),
						(f[r++] = 255 & s)
					for (let e = 0; e < 6; ++e) f[r + e] = c[e]
					return t || (0, u.unsafeStringify)(f)
				}
			},
			237: (e, t, n) => {
				Object.defineProperty(t, "__esModule", { value: !0 }), (t.default = void 0)
				var r = u(n(925)),
					o = u(n(163))
				function u(e) {
					return e && e.__esModule ? e : { default: e }
				}
				var a = (0, r.default)("v3", 48, o.default)
				t.default = a
			},
			925: (e, t, n) => {
				Object.defineProperty(t, "__esModule", { value: !0 }),
					(t.URL = t.DNS = void 0),
					(t.default = function (e, t, n) {
						function r(e, r, a, l) {
							var i
							if (
								("string" == typeof e &&
									(e = (function (e) {
										e = unescape(encodeURIComponent(e))
										const t = []
										for (let n = 0; n < e.length; ++n) t.push(e.charCodeAt(n))
										return t
									})(e)),
								"string" == typeof r && (r = (0, u.default)(r)),
								16 !== (null === (i = r) || void 0 === i ? void 0 : i.length))
							)
								throw TypeError(
									"Namespace must be array-like (16 iterable integer values, 0-255)"
								)
							let d = new Uint8Array(16 + e.length)
							if (
								(d.set(r),
								d.set(e, r.length),
								(d = n(d)),
								(d[6] = (15 & d[6]) | t),
								(d[8] = (63 & d[8]) | 128),
								a)
							) {
								l = l || 0
								for (let e = 0; e < 16; ++e) a[l + e] = d[e]
								return a
							}
							return (0, o.unsafeStringify)(d)
						}
						try {
							r.name = e
						} catch (e) {}
						return (r.DNS = a), (r.URL = l), r
					})
				var r,
					o = n(8),
					u = (r = n(222)) && r.__esModule ? r : { default: r }
				const a = "6ba7b810-9dad-11d1-80b4-00c04fd430c8"
				t.DNS = a
				const l = "6ba7b811-9dad-11d1-80b4-00c04fd430c8"
				t.URL = l
			},
			355: (e, t, n) => {
				Object.defineProperty(t, "__esModule", { value: !0 }), (t.default = void 0)
				var r = a(n(790)),
					o = a(n(319)),
					u = n(8)
				function a(e) {
					return e && e.__esModule ? e : { default: e }
				}
				t.default = function (e, t, n) {
					if (r.default.randomUUID && !t && !e) return r.default.randomUUID()
					const a = (e = e || {}).random || (e.rng || o.default)()
					if (((a[6] = (15 & a[6]) | 64), (a[8] = (63 & a[8]) | 128), t)) {
						n = n || 0
						for (let e = 0; e < 16; ++e) t[n + e] = a[e]
						return t
					}
					return (0, u.unsafeStringify)(a)
				}
			},
			764: (e, t, n) => {
				Object.defineProperty(t, "__esModule", { value: !0 }), (t.default = void 0)
				var r = u(n(925)),
					o = u(n(757))
				function u(e) {
					return e && e.__esModule ? e : { default: e }
				}
				var a = (0, r.default)("v5", 80, o.default)
				t.default = a
			},
			435: (e, t, n) => {
				Object.defineProperty(t, "__esModule", { value: !0 }), (t.default = void 0)
				var r,
					o = (r = n(58)) && r.__esModule ? r : { default: r }
				t.default = function (e) {
					return "string" == typeof e && o.default.test(e)
				}
			},
			464: (e, t, n) => {
				Object.defineProperty(t, "__esModule", { value: !0 }), (t.default = void 0)
				var r,
					o = (r = n(435)) && r.__esModule ? r : { default: r }
				t.default = function (e) {
					if (!(0, o.default)(e)) throw TypeError("Invalid UUID")
					return parseInt(e.slice(14, 15), 16)
				}
			},
		},
		t = {}
	!(function n(r) {
		var o = t[r]
		if (void 0 !== o) return o.exports
		var u = (t[r] = { exports: {} })
		return e[r].call(u.exports, u, u.exports, n), u.exports
	})(136)
})()
