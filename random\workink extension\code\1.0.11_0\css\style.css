@font-face {
	font-family: "<PERSON><PERSON>";
	src: url("fonts/Gilroy-Regular.ttf");
	font-weight: 400;
}

@font-face {
	font-family: "<PERSON>roy";
	src: url("fonts/Gilroy-Medium.ttf");
	font-weight: 500;
}

@font-face {
	font-family: "<PERSON>roy";
	src: url("fonts/Gilroy-Bold.ttf");
	font-weight: 800;
}

@font-face {
	font-family: "Gilroy";
	src: url("fonts/Gilroy-Heavy.ttf");
	font-weight: 900;
}


::-webkit-scrollbar {
	height: 0;
	width: 0;
	/* Remove scrollbar space */
	background: transparent;
	/* Optional: just make scrollbar invisible */
}

/* Optional: show position indicator in red */
::-webkit-scrollbar-thumb {
	background: rgba(0, 0, 0, 0)
}

/* Variables */

:root {
	--color-primary: #009974;

	--thumbnail-width: 190px;
	--thumbnail-height: 106px;
}

html {
	width: 375px !important;
}

* {
	margin: 0;
	padding: 0;
	outline: none;
	border: none;
	background: none;
	font-family: "<PERSON><PERSON>";
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	box-sizing: border-box;
	text-decoration: none;
	-webkit-tap-highlight-color: transparent;
	-webkit-appearance: none;
	-webkit-font-smoothing: antialiased;
}

span {
	display: block;
}

input,
textarea {
	-webkit-user-select: text;
}

html {
	overflow: hidden;
}

body {
	overflow-y: scroll;
	overflow-x: hidden;
	-webkit-overflow-scrolling: touch;
	font-size: var(--size-font-default)
}

.text {
	font-size: 14px;
}

.text.paragraph {
	line-height: 16px;
}

.text > b {
	color: black;
}

.text.translucent {
	color: rgba(0,0,0,0.7);
}

.text.small {
	font-size: 12px;
}

.text.big {
	font-size: 24px;
}

.text.medium {
	font-weight: 500;
	color: black !important;
}

.text.bold {
	font-weight: 600;
	letter-spacing: 0;
}

.text > .text {
	display: inline;
	font-size: inherit;
}