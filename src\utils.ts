export async function sleep(ms: number) {
	return new Promise(resolve => setTimeout(resolve, ms))
}

export function getCallerName(offset = 0) {
	try {
		// Throw an error to capture the stack
		throw new Error()
	} catch (err) {
		const stack = (err as Error).stack?.split("\n")

		if (!stack) throw new Error("Stack not found")

		const match = stack[offset + 2].match(/\s*at (.+?) .+/)

		if (!match || !match[1]) {
			console.log(stack)
			throw new Error("Caller not found in stack ^")
		}

		return match[1]
	}
}

import OriginalJsConfuser from "js-confuser"
import type OriginalJsConfuserType from "../node_modules/js-confuser/src/index.ts"
import type { ObfuscateOptions } from "../node_modules/js-confuser/src/options.ts"
import type { ObfuscationResult } from "../node_modules/js-confuser/src/obfuscationResult.ts"

type CustomJsConfuserType = {
	obfuscate: (code: string, options: ObfuscateOptions) => Promise<ObfuscationResult>
}

type jsConfuserType = Omit<typeof OriginalJsConfuserType, keyof CustomJsConfuserType> & CustomJsConfuserType
const jsConfuser = OriginalJsConfuser as unknown as jsConfuserType

const CACHE_DURATION = 5 * 60 * 1000
const LOCK_GRACE_PERIOD = 1 * 60 * 1000
export async function* obfuscate(code: string) {
	let cachedResult: string | null = null
	let lastUpdate = 0
	

	while (true) {
		const now = Date.now()
		if (!cachedResult || now - lastUpdate >= CACHE_DURATION) {
			const result = await jsConfuser.obfuscate(code, {
				target: "browser",
				preset: "high",
				lock: {
					startDate: now,
					endDate: now + CACHE_DURATION + LOCK_GRACE_PERIOD,
				},
			} as ObfuscateOptions)
			cachedResult = result.code
			lastUpdate = now
		}
		yield cachedResult
	}
}
