import expressWs from "express-ws"
import { Browser, EmptyObject, Page } from "../types.js"
import { BypassError, deductCredits } from "./index.js"
import { obfuscate, sleep } from "../utils.js"
import Websocket from "ws"
import { RequestInterceptionManager } from "puppeteer-intercept-and-modify-requests"
import path from "path"
import { readFileSync, writeFileSync } from "fs"
import { _dirname } from "../index.js"
import { config } from "../config.js"
import * as acorn from "acorn"
import * as walk from "acorn-walk"

/* 
    {
        FOCUS: 'c_focus',
        OFFER_SKIPPED: 'c_offer_skipped',
        KEYAPP_KEY: 'c_keyapp_key',
        TURNSTILE_RESPONSE: 'c_turnstile_response',
        RECAPTCHA_RESPONSE: 'c_recaptcha_response',
        ANNOUNCE: 'c_announce',
        WORKINK_PASS_USE: 'c_workink_pass_use',
        OFFERS_SKIPPED: 'c_offers_skipped',
        ADBLOCKER_DETECTED: 'c_adblocker_detected',
        MONETIZATION: 'c_monetization',
        FOCUS_LOST: 'c_focus_lost',
        WORKINK_PASS_AVAILABLE: 'c_workink_pass_available',
        SOCIAL_STARTED: 'c_social_started',
        MONOCLE: 'c_monocle',
        PING: 'c_ping',
        HCAPTCHA_RESPONSE: 'c_hcaptcha_response',
        BOT_DETECTED: 'c_bot_detected'
    }
    {
        PROXY_DETECTED: 's_prxd',
        START_HCAPTCHA_CHECK: 's_sthc',
        LINK_DESTINATION: 's_lkds',
        REDIRECTION_CANCELED: 's_rdcc',
        LINK_INFO: 's_lkif',
        SOCIAL_DONE: 's_scdn',
        START_TURNSTILE_CHECK: 's_tstc',
        PONG: 's_pgxx',
        SOCIAL_RUNNING: 's_scrn',
        HCAPTCHA_OKAY: 's_hcok',
        PREMIUM_ONLY: 's_prmo',
        RECAPTCHA_OKAY: 's_rcka',
        KEYAPP_KEY_INVALID: 's_kyiv',
        LINK_NOT_FOUND: 's_lknf',
        START_RECAPTCHA_CHECK: 's_strc',
        MONETIZATION: 's_mntz',
        ERROR: 's_errx',
        WORKINK_PASS_LEFT: 's_wkpl'
    }
*/

type Packet =
	// Client packets
	| { type: "SOCIAL_STARTED"; payload: { url: string } }
	| {
			type: "RECAPTCHA_RESPONSE"
			payload: {
				recaptchaResponse: string
			}
	  }
	| {
			type: "TURNSTILE_RESPONSE"
			payload: {
				token: string
			}
	  }
	| { type: "WORKINK_PASS_USE"; payload: EmptyObject }
	| { type: "MONETIZATION"; payload: { type: string; payload: { event: string } } }
	| { type: "PING"; payload: { timestamp: number } }
	// Server packets
	| { type: "PONG"; payload: { timestamp: number } }
	| {
			type: "LINK_INFO"
			payload: {
				id: number
				isAdblockEnabled: boolean
				isDesktopOnly: boolean
				monetizationLevel: string
				monetizations: number[]
				monetizationsNeeded: number
				onlyStaticMonetizations: boolean
				socials: { type: string; url: string }[]
				userId: number
			}
	  }
	| { type: "SOCIAL_DONE"; payload: { url: string } }
	| { type: "SOCIAL_RUNNING"; payload: { time: number; url: string } }
	| {
			type: "LINK_DESTINATION"
			payload: {
				url: string
				usingPass: boolean
				usingPremium: boolean
			}
	  }
	| { type: "START_RECAPTCHA_CHECK"; payload: { siteKey: string } }
	| { type: "ADBLOCKER_DETECTED"; payload: EmptyObject }

/**
 * Maps packet types to their string representations.
 *
 * WORKINK_PASS_USE -> c_workink_pass_use
 *
 * c_workink_pass_use -> WORKINK_PASS_USE
 */
const packetTypeMap: Record<Packet["type"], string> & Record<string, Packet["type"]> = {} as any

// cSpell:disable
const monetizationMap = {
	20: "POPUPS_DESKTOP",
	21: "POPUPS_MOBILE",
	22: "READ_ARTICLES",
	23: "INSTALLER",
	24: "OPERA",
	25: "OPERA_GX",
	27: "BUFF_DESKTOP",
	28: "BUFF_MOBILE",
	29: "BROWSER_EXTENSION",
	32: "NORDVPN",
	33: "RAID_SHADOW_LEGENDS",
	34: "NORTON_ANTIVIRUS",
	36: "LUME_BROWSER_ANDROID",
	37: "PUSH_NOTIFICATIONS",
	38: "PUSH_NOTIFICATIONS_FULLSCREEN",
	39: "TIKTOK",
	40: "INSTALL_APP",
	41: "OPERAGX_MOBILE",
	42: "QUICKSNAP",
	43: "TEMU",
	44: "SHEIN",
	45: "PDF_EDITOR",
	47: "OPERA_GX_IOS",
	48: "W_AUTO_CLICKER",
	49: "W_FLASHLIGHT",
	50: "W_PRANK_CALL_VIDEO",
	51: "W_ANTI_THEFT_MY_PHONE",
	52: "W_FLASHLIGHT_SOS",
	53: "W_AMAZON_SHOPPING",
	54: "W_CASH_EM_ALL",
	55: "W_MOBILE_LEGENDS",
	56: "W_CASH_GIRAFFE",
	57: "BETTERDEALS",
	58: "OPERA_POPUP",
	60: "LDPLAYER",
	61: "PIPPIT",
	62: "ONTHATASS",
	63: "PRIMEVIDEO",
	64: "RIVAGAUCHE",
	65: "LENME",
	66: "TESTERUP",
	67: "XANH",
	68: "TIMO",
	69: "SOLITAIRECASH",
	70: "GAUTHAI",
	71: "EXTERNAL_ARTICLES",
	POPUPS_DESKTOP: 20,
	POPUPS_MOBILE: 21,
	READ_ARTICLES: 22,
	INSTALLER: 23,
	OPERA: 24,
	OPERA_GX: 25,
	BUFF_DESKTOP: 27,
	BUFF_MOBILE: 28,
	BROWSER_EXTENSION: 29,
	NORDVPN: 32,
	RAID_SHADOW_LEGENDS: 33,
	NORTON_ANTIVIRUS: 34,
	LUME_BROWSER_ANDROID: 36,
	PUSH_NOTIFICATIONS: 37,
	PUSH_NOTIFICATIONS_FULLSCREEN: 38,
	TIKTOK: 39,
	INSTALL_APP: 40,
	OPERAGX_MOBILE: 41,
	QUICKSNAP: 42,
	TEMU: 43,
	SHEIN: 44,
	PDF_EDITOR: 45,
	OPERA_GX_IOS: 47,
	W_AUTO_CLICKER: 48,
	W_FLASHLIGHT: 49,
	W_PRANK_CALL_VIDEO: 50,
	W_ANTI_THEFT_MY_PHONE: 51,
	W_FLASHLIGHT_SOS: 52,
	W_AMAZON_SHOPPING: 53,
	W_CASH_EM_ALL: 54,
	W_MOBILE_LEGENDS: 55,
	W_CASH_GIRAFFE: 56,
	BETTERDEALS: 57,
	OPERA_POPUP: 58,
	LDPLAYER: 60,
	PIPPIT: 61,
	ONTHATASS: 62,
	PRIMEVIDEO: 63,
	RIVAGAUCHE: 64,
	LENME: 65,
	TESTERUP: 66,
	XANH: 67,
	TIMO: 68,
	SOLITAIRECASH: 69,
	GAUTHAI: 70,
	EXTERNAL_ARTICLES: 71,
} as const

const monetizationPayloads: Partial<
	Record<keyof typeof monetizationMap, { type: string; payload: { event: string } }>
> = {
	PDF_EDITOR: { type: "pdfeditor", payload: { event: "installed" } },
	READ_ARTICLES: { type: "readArticles2", payload: { event: "read" } },
} as const
// cSpell:enable

const encryptionTokenUpdateInterval = 1000 * 60 * 60
let encryptionTokenOne = "No encryption token found"
let encryptionTokenTwo = "No encryption token found"

function decryptPacket(packetData: string): Packet {
	try {
		let packet
		let l = ""
		const a = encryptionTokenOne.split("").map(p => p.charCodeAt(0)),
			r = packetData.substring(0, 2)
		let d = parseInt(r, 16)
		const o = packetData.substring(2).match(/.{1,2}/g) || []
		for (let p = 0; p < o.length; p++) {
			const m = parseInt(o[p], 16),
				L = a[(p * 2 + d) % a.length],
				j = (m - (p % 8) + 256) % 256,
				R = String.fromCharCode(j ^ L)
			;(l += R), (d = (d * 19 + 29) % 256)
		}
		packet = JSON.parse(l)
		let f = ""
		const b = encryptionTokenTwo.split("").map(p => p.charCodeAt(0)),
			k = packet.payload.substring(0, 2)
		let g = parseInt(k, 16)
		const h = packet.payload.substring(2).match(/.{1,2}/g) || []
		for (let p = 0; p < h.length; p++) {
			const m = parseInt(h[p], 16),
				L = b[(p * 2 + g) % b.length],
				j = (m - (p % 8) + 256) % 256,
				R = String.fromCharCode(j ^ L)
			;(f += R), (g = (g * 19 + 29) % 256)
		}
		packet.payload = JSON.parse(f)

		// "c_workink_pass_use" -> "WORKINK_PASS_USE"
		packet.type = packetTypeMap[packet.type]

		return packet
	} catch (error) {
		throw new BypassError("Error 1")
	}
}

function encryptPacket(packet: Packet): string {
	// "WORKINK_PASS_USE" -> "c_workink_pass_use"
	packet.type = packetTypeMap[packet.type] as any

	try {
		function encrypt(data: string, token: string) {
			let g = ""
			const D = token.split("").map(m => m.charCodeAt(0))
			let M = Date.now() % 256 // Doesn't need to be actual date it can be anything
			g += M.toString(16).padStart(2, "0")
			for (let m = 0; m < data.length; m++) {
				const b = data.charCodeAt(m),
					C = D[(m * 2 + M) % D.length],
					P = ((b ^ C) + (m % 8)) % 256
				;(g += P.toString(16).padStart(2, "0")), (M = (M * 19 + 29) % 256)
			}
			return g
		}

		const encryptedPayload = encrypt(JSON.stringify(packet.payload), encryptionTokenTwo)

		const packetString = JSON.stringify({
			type: packet.type,
			payload: encryptedPayload,
		})

		const encryptedPacket = encrypt(packetString, encryptionTokenOne)

		return encryptedPacket
	} catch (error) {
		throw new BypassError("Error 2")
	}
}

function handleError(ws: Websocket, error: unknown) {
	if (error instanceof BypassError) {
		ws.send("Error: " + error.message)
	} else {
		console.error(error)
	}
	ws.close()
}

function findPacketTypes(ast: acorn.Program) {
	walk.simple(ast, {
		AssignmentExpression(node) {
			// a.something = ...
			if (node.left.type !== "MemberExpression" || node.left.property.type !== "Identifier") {
				return
			}

			// ... = "something"
			if (node.right.type !== "Literal" || typeof node.right.value !== "string") {
				return
			}

			const key = node.left.property.name
			const value = node.right.value

			// c_ or s_
			if (!/^[cs]_/.test(value)) {
				return
			}

			packetTypeMap[key as any] = value as any
			packetTypeMap[value as any] = key as any
		},
	})
}

async function update_(browser: Browser) {
	let page: Page
	try {
		console.log("Getting encryption tokens")

		page = await browser.newPage()

		const client = await page.createCDPSession()
		const interceptManager = new RequestInterceptionManager(client)
		await interceptManager.intercept({
			urlPattern: `https://work.ink/_app/immutable/nodes/3.*`,
			modifyResponse({ body: src }) {
				if (!src) return

				// Save original script to a file for debugging
				writeFileSync(path.join(_dirname, "../workInkReversing/original.js"), src)

				let ast = acorn.parse(src, {
					ecmaVersion: "latest",
					sourceType: "module",
				})

				findPacketTypes(ast)

				let found = false

				walk.simple(ast, {
					ClassDeclaration(node) {
						if (!found) return

						const ctor = node.body.body.find(
							n => n.type === "MethodDefinition" && n.kind === "constructor"
						)
						// Check if this class is the websocket wrapper
						if (!ctor || src!.slice(ctor.start, ctor.end).includes("wss:")) return

						walk.simple(node, {
							VariableDeclaration(node) {
								if (!found) return
								if (node.kind !== "let") return
								if (node.declarations.length < 3) return

								// let _, encryptionTokenOne, encryptionTokenTwo
								const [var1, var2, var3] = node.declarations

								if (
									!var1.init &&
									var2.init &&
									var2.id.type === "Identifier" &&
									var3.init &&
									var3.id.type === "Identifier"
								) {
									console.log(var2.id.name, var3.id.name)
								}
							},
						})
					},
				})

				if (!found) console.error("Failed to find websocket wrapper")

				// Get the name of the websocket wrapper class
				const className = src
					.match(/class \w+(?=.+?wss:\/\/)/g)
					?.pop()
					?.slice(6)

				if (!className) console.error("Failed to find class name")

				// Inject code to get encryption tokens
				src = src.replace(
					new RegExp(
						`class ${className}{constructor\\([a-zA-Z0-9_$]+,[a-zA-Z0-9_$]+\\){(?:[a-zA-Z0-9_$]+\\(this,.+?){26},async [a-zA-Z0-9_$]+=>{.+?let [a-zA-Z0-9_$]+,([a-zA-Z0-9_$]+)=.+?,([a-zA-Z0-9_$]+)=.+?;`
					),
					(substring, ...args) => {
						const varName1 = args[0]
						const varName2 = args[1]

						console.log("Found encryption token variables", varName1, varName2)

						return (
							substring +
							`window.encryptionTokenOne=${varName1};window.encryptionTokenTwo=${varName2};`
						)
					}
				)

				// Save modified script to a file for debugging
				writeFileSync(path.join(_dirname, "../workInkReversing/modified.js"), src)

				return { body: src }
			},
		})

		await page.goto("https://work.ink/1ZRk/ltrgbw20")

		while (true) {
			console.log("Waiting for cloudflare protection")
			await sleep(1000)
			if (!(await page.$(".challenge-wrapper"))) break
		}

		await sleep(2000)

		console.log("Cloudflare protection passed")

		const encryptionTokensHandle = await page.waitForFunction(() => {
			//@ts-expect-error
			if (window.encryptionTokenOne && window.encryptionTokenTwo) {
				//@ts-expect-error
				console.log(window.encryptionTokenOne, window.encryptionTokenTwo) //@ts-expect-error
				return [window.encryptionTokenOne, window.encryptionTokenTwo]
			}
			return false
		})

		const encryptionTokens = (await encryptionTokensHandle.jsonValue()) as [string, string]

		console.log("Encryption tokens found", encryptionTokens)

		encryptionTokenOne = encryptionTokens[0]
		encryptionTokenTwo = encryptionTokens[1]
	} catch (error) {
		console.error("Error getting encryption tokens", error)
	} finally {
		// Temporary delay to try to see why it sometimes fails
		setTimeout(() => page?.close(), 1000 * 60)
	}
}

async function update(browser: Browser) {
	let page: Page
	try {
		console.log("Getting encryption tokens")

		page = await browser.newPage()

		const client = await page.createCDPSession()
		const interceptManager = new RequestInterceptionManager(client)
		await interceptManager.intercept({
			urlPattern: `https://work.ink/_app/immutable/nodes/3.*`,
			modifyResponse({ body: src }) {
				if (!src) return

				// Save original script to a file for debugging
				writeFileSync(path.join(_dirname, "../workInkReversing/original.js"), src)

				let ast = acorn.parse(src, {
					ecmaVersion: "latest",
					sourceType: "module",
				})

				findPacketTypes(ast)

				let found = false

				walk.simple(ast, {
					ClassDeclaration(node) {
						if (!found) return

						const ctor = node.body.body.find(
							n => n.type === "MethodDefinition" && n.kind === "constructor"
						)
						// Check if this class is the websocket wrapper
						if (!ctor || src!.slice(ctor.start, ctor.end).includes("wss:")) return

						walk.simple(node, {
							VariableDeclaration(node) {
								if (!found) return
								if (node.kind !== "let") return
								if (node.declarations.length < 3) return

								// let _, encryptionTokenOne, encryptionTokenTwo
								const [var1, var2, var3] = node.declarations

								if (
									!var1.init &&
									var2.init &&
									var2.id.type === "Identifier" &&
									var3.init &&
									var3.id.type === "Identifier"
								) {
									console.log(var2.id.name, var3.id.name)
								}
							},
						})
					},
				})

				if (!found) console.error("Failed to find websocket wrapper")

				// Get the name of the websocket wrapper class
				const className = src
					.match(/class \w+(?=.+?wss:\/\/)/g)
					?.pop()
					?.slice(6)

				if (!className) console.error("Failed to find class name")

				// Inject code to get encryption tokens
				src = src.replace(
					new RegExp(
						`class ${className}{constructor\\([a-zA-Z0-9_$]+,[a-zA-Z0-9_$]+\\){(?:[a-zA-Z0-9_$]+\\(this,.+?){26},async [a-zA-Z0-9_$]+=>{.+?let [a-zA-Z0-9_$]+,([a-zA-Z0-9_$]+)=.+?,([a-zA-Z0-9_$]+)=.+?;`
					),
					(substring, ...args) => {
						const varName1 = args[0]
						const varName2 = args[1]

						console.log("Found encryption token variables", varName1, varName2)

						return (
							substring +
							`window.encryptionTokenOne=${varName1};window.encryptionTokenTwo=${varName2};`
						)
					}
				)

				// Save modified script to a file for debugging
				writeFileSync(path.join(_dirname, "../workInkReversing/modified.js"), src)

				return { body: src }
			},
		})

		await page.goto("https://work.ink/1ZRk/ltrgbw20")

		while (true) {
			console.log("Waiting for cloudflare protection")
			await sleep(1000)
			if (!(await page.$(".challenge-wrapper"))) break
		}

		await sleep(2000)

		console.log("Cloudflare protection passed")

		//@ts-expect-error
		await page.waitForFunction(() => window.encryptionTokenOne && window.encryptionTokenTwo)

		//@ts-expect-error
		encryptionTokenOne = await page.evaluate(() => window.encryptionTokenOne)
		//@ts-expect-error
		encryptionTokenTwo = await page.evaluate(() => window.encryptionTokenTwo)

		console.log("Encryption tokens found", encryptionTokenOne, encryptionTokenTwo)
	} catch (error) {
		console.error("Error getting encryption tokens", error)
		throw error
	} finally {
		// Temporary delay to try to see why it sometimes fails
		setTimeout(() => page?.close(), 1000 * 60)
	}
}

export async function workInkBypass(app: expressWs.Application, browser: Browser) {
	await update(browser)

	setInterval(async () => {
		await update(browser)
	}, encryptionTokenUpdateInterval)

	const loader = readFileSync(path.join(_dirname, "bypasses/workInk.user.js"), "utf8").replace(
		"{{DOMAIN}}",
		config.domain
	)
	app.get("/workInkLoader", async (req, res) => {
		res.send(loader)
	})

	const obfuscatedScript = obfuscate(
		readFileSync(path.join(_dirname, "bypasses/workInk.client.js"), "utf8").replace(
			"{{DOMAIN}}",
			config.domain
		)
	)
	app.get("/workInk", async (req, res) => {
		res.send((await obfuscatedScript.next()).value)
	})

	app.ws("/workInk", async (ws, req) => {
		try {
			await deductCredits(req, 1, "workInk bypass")

			let linkInfo: Extract<Packet, { type: "s_link_info" }>

			ws.on("message", async message => {
				try {
					const data = message.toString()
					const packet = decryptPacket(data)

					// Ignore pings and pongs
					if (packet.type === "PING" || packet.type === "PONG") {
						return
					}

					console.log("Receiving packet", packet)

					const packetQueue: { packet: Packet; delay: number }[] = [{ packet, delay: 0 }]

					switch (packet.type) {
						case "LINK_INFO": {
							packetQueue.pop()

							let modifiedPacket = packet
							modifiedPacket.payload.isAdblockEnabled = false

							packetQueue.push({
								packet: modifiedPacket,
								delay: 0,
							})

							break
						}

						case "TURNSTILE_RESPONSE": {
							/* // Wait for turnstile to be solved before sending skip
							packetQueue.push({
								packet: {
									type: "WORKINK_PASS_USE",
									payload: {},
								},
								delay: 500,
							}) */

							/* SendMesage c_monetization {
    "type": "readArticles2",
    "payload": {
        "event": "read"
    },
    "s": "FOyWLycLacw35PbZpwK8Q3N6ouw6PBQ2snZHMIDmXrUXoCUXv7XgOiVlrl9NMn2p"




    class ue {
    constructor(s) {
        k(this, "id", 0);
        k(this, op, "monetization");
        k(this, lp, ![]);
        k(this, "isOffer", ![]);
        k(this, cp, []);
        k(this, xp, ![]);
        k(this, dp);
        k(this, fp, () => {
            var s = {
                l: 189,
                j: 195,
                g: 178,
                a: 149,
                F: "UBZ^",
                e: 187,
                d: 142,
                T: 203,
                X: 153,
                o: "dNXV"
            }
              , e = Ve
              , t = st;
            this[t(s.l) + "e"] = !![],
            this[t(s.j) + "ffer"] && this[t(s.g) + e(s.a, s.F) + t(s.e) + "kCl" + t(s.d) + "t"]()["onO" + t(s.T) + "rDone"](this[e(s.X, s.o) + "e"])
        }
        );
        k(this, Ap, () => {
            var s = {
                l: 183,
                j: "L5KM",
                g: 146,
                a: "MGV2",
                F: 167,
                d: "xG7p"
            }
              , e = Ve;
            return this[e(s.l, s.j) + "kIn" + e(s.g, s.a) + e(s.F, s.d) + "t"]
        }
        );
        k(this, up, s => {
            var e = {
                l: 202,
                j: 172,
                g: "dNXV",
                a: 143,
                F: "[JcZ",
                T: 147,
                X: "ging",
                o: 181,
                f: 171,
                G: 168,
                b: 151,
                q: "kNYs",
                B: 208,
                E: 196,
                z: "Y4ga",
                D: 163,
                w: 206,
                t: "DKPv",
                N: 207,
                v: "zhdm",
                S: 201,
                P: 152,
                H: "4iuY",
                O: 178,
                n: 139,
                M: "0sJV",
                V: 155,
                Y: 142,
                m: 184,
                k: 154,
                L: 160,
                y: 197,
                x: "3vpd",
                A0: 156,
                A1: 144,
                A2: 162,
                A3: "JWEY",
                A4: 165,
                A5: 191
            }
              , t = Ve
              , n = st
              , a = {};
            debugger
            a[n(e.l) + "IB"] = t(e.j, e.g) + t(e.a, e.F) + "cLa" + t(e.T, e.X) + n(e.o) + n(e.f) + n(e.G) + t(e.b, e.q) + "ouw6PB" + n(e.B) + t(e.E, e.z) + n(e.D) + "mXr" + t(e.w, e.t) + "CUXv7X" + t(e.N, e.v) + "Vlr" + n(e.S) + t(e.P, e.H) + "p";
            var c = a;
            this[n(e.O) + "Wor" + t(e.n, e.M) + n(e.V) + n(e.Y) + "t"]()[n(e.m) + t(e.k, e.H) + n(e.L) + t(e.y, e.x) + "xbm" + n(e.A0) + "hq"](et[n(e.A1) + t(e.A2, e.A3) + n(e.A4) + n(e.A5)], {
                type: this["name"],
                payload: s,
                s: c["tWZIB"]
            })
        }
        );
        k(this, hp, async () => {}
        );
        k(this, mp, async () => {}
} */

							break
						}
						case "LINK_DESTINATION": {
							packetQueue.pop()

							let modifiedPacket = packet
							modifiedPacket.payload.usingPass = false

							packetQueue.push({
								packet: modifiedPacket,
								delay: 0,
							})

							ws.send("url" + packet.payload.url)
							break
						}

						// Disable adblock and recaptcha checks
						case "ADBLOCKER_DETECTED":
						case "RECAPTCHA_RESPONSE":
						case "START_RECAPTCHA_CHECK": {
							packetQueue.pop()
							break
						}
					}

					for (const { packet, delay } of packetQueue) {
						console.log("Sending packet", packet, "with delay", delay)
						await sleep(delay)
						if (packetTypeMap[packet.type].startsWith("c")) {
							ws.send("toServer" + encryptPacket(packet))
						} else if (packetTypeMap[packet.type].startsWith("s")) {
							ws.send("toClient" + encryptPacket(packet))
						} else {
							throw new Error("Invalid packet type")
						}
					}
				} catch (error) {
					handleError(ws, error)
				}
			})
		} catch (error) {
			handleError(ws, error)
		}
	})
}
