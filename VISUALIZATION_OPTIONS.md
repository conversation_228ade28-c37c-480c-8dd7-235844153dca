# Data Visualization Options for Bypass Statistics

This document outlines different approaches to visualize logging and statistics data for the bypass service.

## 1. Grafana + Prometheus (Recommended for Production)

Best for real-time dashboards, alerting, and professional monitoring.

### Setup Steps:
1. Install dependencies:
```bash
npm install prom-client
```

2. Create metrics file (`src/metrics.ts`):
```typescript
import { register, Counter, Histogram, Gauge } from 'prom-client'

export const bypassAttempts = new Counter({
  name: 'bypass_attempts_total',
  help: 'Total number of bypass attempts',
  labelNames: ['type', 'status', 'user_key_prefix']
})

export const bypassDuration = new Histogram({
  name: 'bypass_duration_seconds',
  help: 'Duration of bypass attempts',
  labelNames: ['type', 'status'],
  buckets: [0.1, 0.5, 1, 2, 5, 10, 30]
})

export const activeConnections = new Gauge({
  name: 'active_websocket_connections',
  help: 'Number of active WebSocket connections'
})

export { register }
```

3. Add metrics endpoint to main server
4. Update logger to increment metrics
5. Set up Grafana to scrape `/metrics` endpoint

### Pros:
- Professional dashboards
- Alerting capabilities
- Historical data retention
- Industry standard

### Cons:
- More complex setup
- Requires additional services

## 2. Simple Web Dashboard (Easiest Start)

Create a basic HTML dashboard using Chart.js that fetches from existing `/stats` endpoint.

### Features:
- Real-time statistics display
- Interactive charts (doughnut, bar, line)
- Auto-refresh every 30 seconds
- Responsive design
- No additional dependencies

### Implementation:
- Single HTML file with embedded JavaScript
- Uses Chart.js from CDN
- Fetches data from existing `/stats` API
- Serves from `/dashboard` route

### Pros:
- Quick to implement
- No additional services needed
- Customizable
- Works with existing code

### Cons:
- Limited features compared to Grafana
- No historical data storage
- Basic alerting only

## 3. Grafana Cloud (Free Tier)

Use Grafana's hosted service with free tier limits:
- 10,000 metrics per month
- 50GB logs per month
- 14-day retention

### Setup:
1. Sign up at grafana.com
2. Use Prometheus setup from option 1
3. Configure Grafana Cloud to scrape your `/metrics` endpoint
4. Import pre-built dashboards

### Pros:
- No infrastructure management
- Professional features
- Free tier available

### Cons:
- Data limits on free tier
- External dependency
- Potential privacy concerns

## 4. ELK Stack (Elasticsearch + Kibana)

For advanced log analysis and search capabilities.

### Docker Compose Setup:
```yaml
version: '3.8'
services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
    ports:
      - "9200:9200"
  
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
```

### Features:
- Full-text log search
- Advanced filtering
- Custom dashboards
- Log aggregation

### Pros:
- Powerful log analysis
- Great for debugging
- Flexible querying

### Cons:
- Resource intensive
- Complex setup
- Overkill for simple stats

## 5. Other Free Options

### Datadog (Free Tier)
- 5 hosts free
- 1-day retention
- Good for small projects

### New Relic (Free Tier)
- 100GB/month free
- Basic dashboards
- Application monitoring

### Uptime Kuma
- Self-hosted monitoring
- Simple setup
- Good for uptime tracking

## Recommendation

**Start with Option 2 (Simple Web Dashboard)** for immediate results, then consider upgrading to **Option 1 (Grafana + Prometheus)** as your needs grow.

### Migration Path:
1. Implement simple dashboard first (1-2 hours)
2. Add Prometheus metrics alongside existing stats (2-3 hours)
3. Set up Grafana when you need advanced features (4-6 hours)

### What to Track:
- Bypass attempts by type
- Success/failure rates
- Response times
- Error types and frequencies
- User activity (anonymized)
- System resource usage
- API endpoint performance

### Sample Metrics to Monitor:
- `bypass_attempts_total{type="workInk", status="success"}`
- `bypass_duration_seconds{type="workInk"}`
- `active_websocket_connections`
- `api_requests_total{endpoint="/workInk"}`
- `errors_total{type="timeout"}`

This approach gives you flexibility to start simple and scale up as needed.