{"version": "6", "dialect": "sqlite", "id": "d0e7a948-df55-4120-bbf4-2669a94dbd31", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"users": {"name": "users", "columns": {"key": {"name": "key", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "credits": {"name": "credits", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "expiration": {"name": "expiration", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "(unixepoch())"}, "firstUsed": {"name": "firstUsed", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "lastUsed": {"name": "lastUsed", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "lastUsedReason": {"name": "lastUsedReason", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}