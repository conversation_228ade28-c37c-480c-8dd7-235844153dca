import fs from "node:fs"
import { LogLevel } from "./logger.js"

export const configFile = "config.json"

export type Config = {
	readonly port: number
	readonly db: string
	readonly domain: string
	readonly logFile: string
	readonly logLevel: LogLevel
	readonly httpLogFile: string
}

export const defaultConfig: Readonly<Config> = {
	port: 80,
	db: "file:local.db",
	domain: "http://localhost",
	logFile: "logs.log",
	logLevel: "debug",
	httpLogFile: "http.log",
}

export let config: Config

export function loadConfig() {
	if (!fs.existsSync(configFile)) {
		fs.writeFileSync(configFile, JSON.stringify(defaultConfig, null, 4))
	}

	const file = fs.readFileSync(configFile, "utf8")
	const json = JSON.parse(file || "{}")
	config = Object.assign(defaultConfig, json)

	// Repair config
	if (config !== json) {
		fs.writeFileSync(configFile, JSON.stringify(config, null, 4))
	}
}
loadConfig()
