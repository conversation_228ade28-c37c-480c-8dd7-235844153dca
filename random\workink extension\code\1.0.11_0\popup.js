;(async () => {
	const e = (await chrome.storage.local.get("fingerprint")).fingerprint,
		t = await fetch(`https://pass.work.ink/passes/${encodeURIComponent(e)}`),
		s = await t.json()
	;(document.getElementById("passes-left").innerHTML = s.passesLeft),
		console.log("Passes left: " + s.passesLeft)
})()
const showMoreButton = document.querySelector(".showmore"),
	listDiv = document.querySelector(".list")
;(listDiv.style.display = "none"),
	showMoreButton.addEventListener("click", () => {
		"none" === listDiv.style.display
			? ((listDiv.style.display = "block"), (showMoreButton.textContent = "Show Less"))
			: ((listDiv.style.display = "none"), (showMoreButton.textContent = "Show More"))
	})
