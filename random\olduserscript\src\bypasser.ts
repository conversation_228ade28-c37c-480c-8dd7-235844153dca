// ==UserScript==
// @name         No Name Bypass
// @namespace    http://tampermonkey.net/
// @version      2025-06-11
// @description  Bypass everything
// <AUTHOR> (thomass47. on discord)
// @match        https://work.ink/*
// @match        https://key.getswift.gg/*
// @match        https://linkvertise.com/*
// @icon         data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==
// @grant        unsafeWindow
// @grant        GM_addStyle
// ==/UserScript==

;(async function () {
	// Config
	const config = {
		url: "http://localhost/bypass?url=",
		key: "aaa",
	}

	// Ui
	const overlay = document.createElement("div")
	overlay.style.display = "flex"
	overlay.style.color = "white"
	overlay.style.backgroundColor = "rgba(0, 0, 0, 0.95)"
	overlay.style.justifyContent = "center"
	overlay.style.alignItems = "center"
	overlay.style.flexDirection = "column"
	overlay.style.position = "fixed"
	overlay.style.top = "0"
	overlay.style.bottom = "0"
	overlay.style.left = "0"
	overlay.style.right = "0"
	overlay.style.zIndex = "2147483648"

	const overlayText = document.createElement("div")
	overlayText.textContent = "You're not supposed to see this"
	overlayText.style.textAlign = "center"

	const overlaySpinner = document.createElement("div")
	overlaySpinner.style.display = "none"
	overlaySpinner.style.width = "3rem"
	overlaySpinner.style.height = "3rem"
	overlaySpinner.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid" style="shape-rendering: auto; display: block; background: transparent;" xmlns:xlink="http://www.w3.org/1999/xlink"><g><path stroke="none" fill="#ffffff" d="M20 50A30 30 0 0 0 80 50A30 32 0 0 1 20 50"><animateTransform values="0 50 51;360 50 51" keyTimes="0;1" repeatCount="indefinite" dur="1s" type="rotate" attributeName="transform"></animateTransform></path><g></g></g></svg>`

	overlay.appendChild(overlayText)
	overlay.appendChild(overlaySpinner)
	document.body.appendChild(overlay)
	setInterval(() => {
		document.body.appendChild(overlay)
	}, 100) // Keep on top

	function disableOverlay() {
		overlay.style.display = "none"
	}

	function log(text: string, loading = true) {
		console.log(text)
		overlayText.innerHTML = text.replaceAll("\n", "<br>")
		overlaySpinner.style.display = loading ? "block" : "none"
	}

	function loadingBar(duration: number) {
		const barWrapper = document.createElement("div")
		barWrapper.style.width = "30%"
		barWrapper.style.height = "2.5%"
		barWrapper.style.backgroundColor = "rgb(69,69,69)"
		barWrapper.style.borderRadius = "5px"

		const bar = document.createElement("div")
		bar.style.width = "0%"
		bar.style.height = "100%"
		bar.style.backgroundColor = "#00FFFF"

		barWrapper.appendChild(bar)
		overlay.appendChild(barWrapper)

		const start = Date.now()
		function update() {
			if (Date.now() - start >= duration) {
				barWrapper.remove()
				return
			}
			bar.style.width = `${((Date.now() - start) / duration) * 100}%`
			requestAnimationFrame(update)
		}
		update()
	}

	// Funcs
	async function sleep(ms: number) {
		return new Promise(resolve => setTimeout(resolve, ms))
	}

	async function getElementByQuery(query: string, wait = true): Promise<HTMLElement | undefined> {
		function findElement() {
			return document.querySelector(query)
		}

		let element = findElement()

		while ((wait && !element) || !(element instanceof HTMLElement)) {
			console.log("element not found, waiting")
			await sleep(100)
			element = findElement()
		}
		console.log("found element", element)

		return element
	}
	async function clickElementByQuery(query: string, wait = true): Promise<boolean> {
		const element = await getElementByQuery(query, wait)
		if (!element) return false
		element.click()
		return true
	}

	async function getElementByText<T extends keyof HTMLElementTagNameMap>(
		tag: T,
		text: string,
		wait = true,
		exact = true
	): Promise<HTMLElementTagNameMap[T] | undefined> {
		function findElement() {
			return Array.from(document.querySelectorAll(tag)).find(el =>
				exact ? el.textContent === text : el.textContent?.includes(text)
			)
		}

		let element = findElement()

		while (wait && !element) {
			console.log("element not found, waiting")
			await sleep(100)
			element = findElement()
		}
		console.log("found element", element)

		return element
	}
	async function clickElementByText<T extends keyof HTMLElementTagNameMap>(
		tag: T,
		text: string,
		wait = true,
		exact = true
	): Promise<boolean> {
		const element = await getElementByText(tag, text, wait, exact)
		if (!element) return false
		element.click()
		return true
	}

	async function getElementByChildSpanText<T extends keyof HTMLElementTagNameMap>(
		tag: T,
		text: string,
		wait = true,
		exact = true
	): Promise<HTMLElementTagNameMap[T] | undefined> {
		function findElement() {
			return Array.from(document.querySelectorAll(tag)).find(el => {
				const span = el.querySelector("span")
				if (!span) return false
				return exact ? span.textContent === text : span.textContent?.includes(text)
			})
		}

		let element = findElement()

		while (wait && !element) {
			console.log("element not found, waiting")
			await sleep(100)
			element = findElement()
		}
		console.log("found element", element)

		return element
	}
	async function clickElementByChildSpanText<T extends keyof HTMLElementTagNameMap>(
		tag: T,
		text: string,
		wait = true,
		exact = true
	): Promise<boolean> {
		const element = await getElementByChildSpanText(tag, text, wait, exact)
		if (!element) return false
		element.click()
		return true
	}

	async function waitUntil(condition: () => boolean): Promise<boolean> {
		while (!condition()) {
			console.log("waiting")
			await sleep(100)
		}
		return true
	}

	function redirect(url: string) {
		console.log("Redirecting to", url)
		location.href = url // Broken on luarmor
		/* history.pushState({}, "", url) */
	}

	async function convertPopupToRedirect(keepPopup = false) {
		const originalOpen = window.open
		unsafeWindow.open = function (url) {
			console.log("Caught window.open, url:", url)
			if (url) redirect(url.toString())

			if (keepPopup) return originalOpen(url)
			else return null // Prevent popup
		}
	}

	function getReferrer(): URL | undefined {
		try {
			return new URL(document.referrer)
		} catch (error) {
			console.log(error)
			return
		}
	}

	// Bypass
	const referrer = getReferrer()
	switch (location.hostname) {
		case "key.getswift.gg": {
			disableOverlay()
			;(async function () {
				function checkTurnstile() {
					const turnstile = document.querySelector(
						"[name=cf-turnstile-response]"
					) as HTMLInputElement
					if (turnstile && turnstile.value !== "") {
						return true
					}
					return false
				}

				function checkHCaptcha() {
					const hcaptcha = document.querySelector("[data-hcaptcha-response]")
					if (hcaptcha && hcaptcha.getAttribute("data-hcaptcha-response") !== "") {
						return true
					}
					return false
				}

				// Only show captcha area
				GM_addStyle(`
        .checkpoint-container {
            background: transparent !important;
            border: none !important;
            box-shadow: none !important;
        }
        .header, .instruction-area, .continue-button, .footer {
            display: none !important;
        }
        `)

				while (!checkTurnstile() && !checkHCaptcha()) {
					console.log("captcha not solved, waiting")
					await sleep(50)
				}
				console.log("captcha solved, continuing")
				;(document.querySelector(".continue-button") as HTMLButtonElement).click() // Click the continue button
			})()
			break
		}

		default: {
			log("bypassing")

			type BypassResponse =
				| {
						bypassed: string
						tokens: string | null
						expiration: Date | null
						error: false
				  }
				| {
						message: string
						error: true
				  }

			const result = await fetch(config.url + location.href, {
				headers: {
					"x-api-key": config.key,
				},
			})
			const json = (await result.json()) as BypassResponse

			if (json.error) {
				log(json.message, false)
			} else {
				log("Redirecting to " + json.bypassed)
				redirect(json.bypassed)
			}
			break
		}
	}
})()
