// ==UserScript==
// @match        https://key.getswift.gg/ks/checkpoint/*
// @grant        GM_addStyle
// ==/UserScript==

;(async function () {
	"use strict"

	function sleep(ms: number) {
		return new Promise(resolve => setTimeout(resolve, ms))
	}

	function checkTurnstile() {
		const turnstile = document.querySelector("[name=cf-turnstile-response]") as HTMLInputElement
		if (turnstile && turnstile.value !== "") {
			return true
		}
		return false
	}

	function checkHCaptcha() {
		const hcaptcha = document.querySelector("[data-hcaptcha-response]")
		if (hcaptcha && hcaptcha.getAttribute("data-hcaptcha-response") !== "") {
			return true
		}
		return false
	}

	async function main() {
		// Only show captcha area
		GM_addStyle(`
        .checkpoint-container {
            background: transparent !important;
            border: none !important;
            box-shadow: none !important;
        }
        .header, .instruction-area, .continue-button, .footer {
            display: none !important;
        }
        `)

		while (!checkTurnstile() && !checkHCaptcha()) {
			console.log("captcha not solved, waiting")
			await sleep(50)
		}
		console.log("captcha solved, continuing")
		;(document.querySelector(".continue-button") as HTMLButtonElement).click() // Click the continue button
	}

	await main()
})()
