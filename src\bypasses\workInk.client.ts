class MessageEvent2 extends MessageEvent<any> {}

const proxyURL = new URL("{{DOMAIN}}")
proxyURL.protocol = proxyURL.protocol === "http:" ? "ws:" : "wss:"
proxyURL.pathname = "/workInk"
proxyURL.searchParams.set("x-api-key", "public")

let bypassed: string | null = null

const OriginalWebSocket = window.WebSocket
// @ts-expect-error
window.WebSocket = function (url: string | URL, protocols?: string | string[] | undefined) {
	console.log("Creating websocket", url)
	if (!url.toString().includes("wss://work.ink/_api/v2/ws")) return new OriginalWebSocket(url, protocols)

	const original = new OriginalWebSocket(url, protocols)
	const proxy = new OriginalWebSocket(proxyURL)

	const originalSend = original.send.bind(original)

	original.send = function (data) {
		proxy.send(data)
	}

	original.addEventListener("message", function (event) {
		if (event instanceof MessageEvent2) return
		proxy.send(event.data)
		event.stopImmediatePropagation()
	})

	proxy.addEventListener("message", async function (event) {
		const data = event.data
		if (typeof data !== "string") throw 1
		if (data === "") throw 2

		if (data.startsWith("toServer")) {
			originalSend(data.slice(8))
		} else if (data.startsWith("toClient")) {
			original.dispatchEvent(new MessageEvent2("message", { data: data.slice(8) }))
		} else if (data.startsWith("url")) {
			bypassed = data.slice(3)
		} else if (data.startsWith("Error: ")) {
			console.error("Error received: ", data.slice(7))
		}
	})
	return original
}

// Preserve prototype and constants
window.WebSocket.prototype = OriginalWebSocket.prototype
Object.keys(OriginalWebSocket).forEach(key => {
	// @ts-expect-error
	window.WebSocket[key] = OriginalWebSocket[key]
})

function update(timestamp: DOMHighResTimeStamp) {
	const button = document.querySelector(".accessBtn") as HTMLDivElement
	if (button) {
		if (bypassed) {
			button.textContent = "Click me"
			document.currentScript?.remove()
		} else {
			button.textContent = "Wait"
		}
	}
	requestAnimationFrame(update)
}

requestAnimationFrame(update)
