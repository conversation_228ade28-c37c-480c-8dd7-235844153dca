;(() => {
	"use strict"
	;({
		624: function () {
			;(
				(this && this.__awaiter) ||
				function (n, i, t, o) {
					return new (t || (t = Promise))(function (e, l) {
						function d(n) {
							try {
								s(o.next(n))
							} catch (n) {
								l(n)
							}
						}
						function a(n) {
							try {
								s(o.throw(n))
							} catch (n) {
								l(n)
							}
						}
						function s(n) {
							var i
							n.done
								? e(n.value)
								: ((i = n.value),
								  i instanceof t
										? i
										: new t(function (n) {
												n(i)
										  })).then(d, a)
						}
						s((o = o.apply(n, i || [])).next())
					})
				}
			)(void 0, void 0, void 0, function* () {
				var n
				window.location.href.includes("www.google.com") &&
					(document.head.innerHTML += "<style>#taw { display: none !important; }</style>")
				const i =
					null !==
						(n = (yield chrome.storage.local.get(["adblockerInstalled"])).adblockerInstalled) &&
					void 0 !== n
						? n
						: void 0
				if (i) {
					const n = `\n            <div style="z-index: 100000; font-family: Arial; color: black; display: flex; position: fixed; right: 25px; bottom: 25px; width: 420px; background: white; border-radius: 16px; box-shadow: 0px 0px 23px -8px rgba(0,0,0,0.71);">\n                <div style="padding: 16px 32px; display: flex; background: #009974; margin-right: 16px; border-top-left-radius: 16px; border-bottom-left-radius: 16px;">\n                    <div style="color: white; align-self: center; font-size: 20px; font-weight: bolder">\n                        Hey!\n                    </div>\n                </div>\n\n                <div style="margin: 16px 32px 16px 0; font-size: 16px;">\n                    Looks like you have <b>${i} installed</b>. Work.ink Pass <b>will not work while this extension is installed</b>. Please uninstall it to collect passes.\n                </div>\n            </div>\n        `
					document.body.innerHTML += n
				}
			})
		},
	})[624]()
})()
