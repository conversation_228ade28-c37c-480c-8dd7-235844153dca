async function _onmessage(packet) {
	let decodedPacket
	try {
		let l = ""
		const a = this.encryptionTokenOne.split("").map(p => p.charCodeAt(0)),
			r = packet.data.substring(0, 2)
		let d = parseInt(r, 16)
		const o = packet.data.substring(2).match(/.{1,2}/g) || []
		for (let p = 0; p < o.length; p++) {
			const m = parseInt(o[p], 16),
				L = a[(p * 2 + d) % a.length],
				j = (m - (p % 8) + 256) % 256,
				R = String.fromCharCode(j ^ L)
			;(l += R), (d = (d * 19 + 29) % 256)
		}
		decodedPacket = JSON.parse(l)
		let f = ""
		const b = this.encryptionTokenTwo.split("").map(p => p.charCodeAt(0)),
			k = decodedPacket.payload.substring(0, 2)
		let g = parseInt(k, 16)
		const h = decodedPacket.payload.substring(2).match(/.{1,2}/g) || []
		for (let p = 0; p < h[e(n.E)]; p++) {
			const m = parseInt(h[p], 16),
				L = b[(p * 2 + g) % b.length],
				j = (m - (p % 8) + 256) % 256,
				R = String.fromCharCode(j ^ L)
			;(f += R), (g = (g * 19 + 29) % 256)
		}
		decodedPacket.payload = JSON.parse(f)
	} catch (l) {
		console[e(n.h)]("Cannot parse message: "), console.error(l)
		return
	}
	switch (((this[e(n.s) + e(n.q) + e(n.Q)] = Date[e(n.l)]()), decodedPacket[e(n.N)])) {
		case $e.ERROR:
			this[e(n.w)](decodedPacket.payload)
			break
		case $e[e(n.I) + e(n.y)]:
			this[e(n.n) + e(n.B)](decodedPacket.payload)
			break
		case $e.LINK_INFO:
			const l = decodedPacket[e(n.v)]
			if (
				((this.linkInfo = l),
				(this[e(n.J) + e(n.i)] =
					l["monetizationLevel"] != null ? parseFloat(l[e(n.J) + "onLevel"].toString()) : 0),
				(this[e(n.J) + e(n.d)] = l["monetizationsNeeded"] ?? 0),
				l[e(n.j) + e(n.k) + "ons"])
			)
				for (let a = 0; a < l["monetizationsNeeded"]; a++) {
					const r = this[e(n.P) + e(n.O) + e(n.z)](l[e(n.J) + e(n.t)][a])
					if (!(!r || !r.isEligible())) {
						await r[e(n.Z)]()
						for await (const d of r[e(n.G)]) {
							const o = this[e(n.P) + e(n.O) + e(n.m)](d)
							!o || !o[e(n.K)]() || (await o[e(n.Z)](), this[e(n.A) + e(n.U)][e(n.X)](r))
						}
						this[e(n.A) + "ons"][e(n.X)](r)
					}
				}
			else
				for (let a = 0; a < l["monetizati" + e(n.F)].length; a++) {
					const r = this[e(n.M) + e(n.O) + e(n.m)](l[e(n.J) + e(n.F)][a])
					if (!(!r || !r[e(n.S)]())) {
						await r[e(n.f)]()
						for await (const d of r.alsoLoad) {
							const o = this[e(n.P) + e(n.O) + "nstance"](d)
							!o || !o[e(n.K)]() || (await o[e(n.a)](), this["monetizations"][e(n.X)](r))
						}
						this[e(n.A) + e(n.g0)][e(n.X)](r)
					}
				}
			;(this[e(n.g1) + "rs"] =
				this[e(n.g2) + "onsCount"] - this[e(n.g3) + e(n.g4)][e(n.g5)](a => !a[e(266)])[e(n.g6)]),
				this[e(n.g7)](decodedPacket[e(n.r)])
			break
		case $e[e(n.g8) + e(n.g9)]:
			this[e(n.gg) + e(n.ge)]()
			break
		case $e["PROXY_DETE" + e(n.gb)]:
			this["onProxyDet" + e(n.gT)]()
			break
		case $e[e(n.gD) + e(n.gY)]:
			this["onRedirect" + e(n.gH) + "d"]()
			break
		case $e["SOCIAL_DONE"]:
			this["onSocialDone"](decodedPacket[e(n.v)])
			break
		case $e[e(n.gE) + e(n.gW)]:
			this[e(n.gC) + e(n.gc)](decodedPacket[e(n.v)])
			break
		case $e["START_HCAP" + e(n.gu)]:
			this["onStartHca" + e(n.gx)]()
			break
		case $e["START_RECAPTCHA_CHECK"]:
			this[e(n.gp) + e(n.gR)](decodedPacket[e(n.gV)])
			break
		case $e[e(n.gr) + e(n.go)]:
			break
		case $e[e(n.gh) + "SS_LEFT"]:
			this["onWorkinkP" + e(n.gL)](decodedPacket[e(n.gs)])
			break
		case $e["MONETIZATION"]:
			this["_onMonetiz" + e(n.gq) + "ge"](decodedPacket[e(n.gQ)])
	}
}
