import expressWs from "express-ws"
import { <PERSON><PERSON><PERSON> } from "../types.js"
import { workInkBypass } from "./workInk.js"
import { db, schema } from "../db/index.js"
import { eq } from "drizzle-orm"
import { type Request } from "express"

export class BypassError extends Error {}

/**
 * Deducts credits from the user
 */
export async function deductCredits(key: string, amount: number, reason: string): Promise<void>
export async function deductCredits(request: Request, amount: number, reason: string): Promise<void>
export async function deductCredits(keyOrRequest: string | Request, amount: number, reason: string) {
	let key: string

	if (typeof keyOrRequest === "string") {
		key = keyOrRequest
	} else {
		const _key = keyOrRequest.headers["x-api-key"] || keyOrRequest.query["x-api-key"]
		if (typeof _key !== "string") {
			throw new BypassError("Missing API key")
		}
		key = _key
	}

	const user = await db.query.users.findFirst({ where: eq(schema.users.key, key) })
	if (!user) {
		throw new BypassError("Invalid key")
	}

	if (user.expiration !== null && user.expiration < new Date()) {
		throw new BypassError("Expired key")
	}

	if (user.credits !== null && user.credits < amount) {
		throw new BypassError("No credits left")
	}

	if (user.credits !== null) user.credits -= amount

	if (user.firstUsed === null) user.firstUsed = new Date()
	user.lastUsed = new Date()
	user.lastUsedReason = reason

	await db.update(schema.users).set(user).where(eq(schema.users.key, key))
}

export async function applyBypasses(app: expressWs.Application, browser: Browser) {
	await workInkBypass(app, browser)
}
