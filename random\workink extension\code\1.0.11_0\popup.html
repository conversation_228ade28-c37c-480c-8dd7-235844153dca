<!DOCTYPE html>

<html>

<head>
    <title>Work.Ink Pass</title>
    <link rel="stylesheet" href="css/style.css">
</head>

<body>
    <div class="wrap">
        <div class="top">
            <img class="logo" alt="logo" src="media/logo.svg" />
        </div>
        <div class="card main">
            <div class="icon">
                <img alt="icon" src="media/icons/ticket.svg" />
            </div>
            <div class="count">
                <span class="text translucent">Passes Left</span>
                <span class="text big bold" id="passes-left">
                    <img src="loader.svg" alt="Loading..." style="height: 32px; margin-bottom: -10px; margin-left: -8px;">
                </span>
            </div>
        </div>
        <div class="card">
            <span class="text title medium">How It Works</span>
            <span class="text translucent paragraph small">
                Work.ink allows you to skip work.ink redirections by monetizing your
                browsing activity
            </span>
            <div style="display: none" class="list">
                <span class="text translucent small">
                    Your <span class="text medium">browsing experience</span> does not
                    change
                </span>
                <span class="text translucent small">
                    You get <span class="text medium">ad-free redirections</span> on
                    work.ink
                </span>
                <span class="text translucent small">
                    Whenever you buy something or register somewhere, you <span class="text medium">support a
                        creator</span>
                </span>
            </div>
            <button class="showmore">Show More</button>
        </div>
        <span class="smallprint">
            Privacy Notice: Data is saved anonymously to our Key-Value-Database for 4 hours. Data includes the timestamp
            of
            visiting a site, the hostname of a website and your browsers fingerprint. If you disable this addon this
            data
            will be gone after 4 hours.
        </span>
    </div>

    <script src="popup.js"></script>

    <style>
        .wrap {
            height: 100%;
            width: 100%;
            background: #f6f6f6;
        }

        .top {
            background: var(--color-primary);
            height: 96px;
            margin-bottom: -32px;
            padding: 0 24px;
            display: flex;
            align-items: center;
            padding-bottom: 32px;
        }

        .top .logo {
            height: 15px;
        }

        .card {
            width: calc(100% - 48px);
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 16px;
            box-shadow: rgba(0, 0, 0, 0.1) -4px 9px 25px -6px;
            margin-bottom: 24px;
        }

        .card.main {
            display: flex;
            align-items: center;
        }

        .card.main .icon {
            height: 40px;
            width: 40px;
            background: var(--color-primary);
            padding: 10px;
            border-radius: 99px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .count {
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .text.title {
            margin-bottom: 12px;
        }

        .list {
            margin-top: 12px;
        }

        .list>span {
            padding-left: 12px;
            position: relative;
        }

        .list>span:before {
            content: "";
            top: 5px;
            left: 0px;
            height: 2.5px;
            width: 2.5px;
            border-radius: 99px;
            background: black;
            position: absolute;
            opacity: 0.7;
        }

        .list>span+span {
            margin-top: 8px;
        }

        button.showmore {
            font-size: 12px;
            font-weight: 600;
            color: var(--color-primary);
            cursor: pointer;
            margin-top: 8px;
        }

        .smallprint {
            padding: 0 24px;
            font-size: 10px;
            opacity: 0.45;
            line-height: 12px;
            padding-bottom: 24px;
        }
    </style>
</body>

</html>