import { RequestInterceptionManager } from "puppeteer-intercept-and-modify-requests"
import { pcall, sleep } from "./utils.js"
import { <PERSON><PERSON><PERSON> } from "./types.js"

export class BypassError extends Error {}

export type Bypass = {
	urlPattern: RegExp
	execute: (browser: <PERSON>rowser, url: string) => Promise<string>
}

const bypasses: Bypass[] = [
	{
		urlPattern: /https:\/\/work.ink\/.*\/.*/,
		execute: async (browser, url) => {
			return "a"
		},
	},
]

export default {
	workInk: async (browser: Browser, url: string): Promise<string> => {
		const page = await browser.newPage()

		const payload = `
window.lmao=this
let first = true
window.open = function (...args) {
	const url = args[0]?.toString()
	if (!url) return null
	if (first) {
        first = false
        location.href = url
    }
	return null
} 
setInterval(() => {
    lmao.sendMessage("c_social_started", {url: 'https://discord.gg/ligma'})
    lmao.sendMessage("c_social_started", {url: 'https://www.youtube.com/shorts/L6fAnnG7Nmo'})
    lmao.sendMessage('c_workink_pass_use',{})
    const button = document.querySelector(".accessBtn")
    if (button) button.click()
}, 200)
setTimeout(() => {
	location.href = location.href
}, 5500)
`

		const client = await page.createCDPSession()
		const interceptManager = new RequestInterceptionManager(client)
		await interceptManager.intercept({
			urlPattern: `https://work.ink/_app/immutable/nodes/*`,
			modifyResponse({ body }) {
				if (!body) return
				body = body.replace(
					/(class ..{constructor\(.,.\){)(..\(this,..,window.location)/,
					`$1;${payload};$2`
				)
				return { body }
			},
		})

		await page.goto(url)

		while (true) {
			console.log("1")
			await page.waitForNavigation()
			await sleep(1000)
			if (!(await page.$(".challenge-wrapper"))) break
		}

		console.log("2")

		/* await page.waitForFunction(() => document.querySelector(".turnstile-container") === null) */

		const start = Date.now()

		const bypass = page.evaluate(() => {
			return new Promise((resolve, reject) => {
				window.open = function (...args) {
					const url = args[0]?.toString()
					if (!url) return null
					if (url.includes("https://outgoing.work.ink/")) reject("Missed url")
					resolve(url)
					return null
				}
				setInterval(() => {
					/* window.lmao.sendMessage("c_social_started", { url: "https://discord.gg/velocityide" })
					window.lmao.sendMessage("c_workink_pass_use", {}) */
					const buttons = [
						...document.querySelectorAll(".accessBtn"),
						/* ...document.querySelectorAll("button"), */
						...document.querySelectorAll('.qc-cmp2-summary-buttons>button[mode="primary"]'),
					]
					for (const button of buttons) {
						/* if (button.textContent?.trim() === "Premium Login") continue
						if (button.textContent?.trim() === "Install") continue */
						if (button instanceof HTMLButtonElement && button.disabled) continue
						console.log(button)
						;(button as HTMLElement).click()
					}
				}, 100)
				setTimeout(() => {
					reject("Timeout")
				}, 10 * 1000)
			})
		})

		const { result, error } = pcall(async () => await bypass)

		if (error) {
			console.log(error, result)
		} else {
			console.log(error, await result)
		}

		const end = Date.now()

		await page.close()

		console.log(end - start + "ms")

		/* 
		// Wait for captcha
		await page.waitForFunction(() => document.querySelector(".turnstile-container") === null)

		await page.click(".accessBtn")

		await sleep(11 * 1000)
		await clickButton("Continue With Ads")

		await sleep(1 * 1000)
		await page.click(".accessBtn")

		await sleep(21 * 1000)
		await clickButton("Access Link")
 */
		return "a"
	},
}
