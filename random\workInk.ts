import { RequestInterceptionManager } from "puppeteer-intercept-and-modify-requests"
import { <PERSON>rows<PERSON>, EmptyObject } from "../types.js"
import { BypassError, log, pcall, sleep } from "../utils.js"
import expressWs from "express-ws"

export type EncryptionTokens = [string, string]

/* 
    Every client packet type

    var vt = (r => (r.ANNOUNCE = "c_announce",
    r.MONETIZATION = "c_monetization",
    r.SOCIAL_STARTED = "c_social_started",
    r.RECAPTCHA_RESPONSE = "c_recaptcha_response",
    r.HCAPTCHA_RESPONSE = "c_hcaptcha_response",
    r.TURNSTILE_RESPONSE = "c_turnstile_response",
    r.ADBLOCKER_DETECTED = "c_adblocker_detected",
    r.FOCUS_LOST = "c_focus_lost",
    r.OFFERS_SKIPPED = "c_offers_skipped",
    r.FOCUS = "c_focus",
    r.WORKINK_PASS_AVAILABLE = "c_workink_pass_available",
    r.WORKINK_PASS_USE = "c_workink_pass_use",
    r.PING = "c_ping",
 */
/* 
    Every server packet type

    var dt = (r => (r.ERROR = "s_error",
    r.LINK_INFO = "s_link_info",
    r.MONETIZATION = "s_monetization",
    r.SOCIAL_DONE = "s_social_done",
    r.SOCIAL_RUNNING = "s_social_running",
    r.LINK_DESTINATION = "s_link_destination",
    r.START_RECAPTCHA_CHECK = "s_start_recaptcha_check",
    r.START_HCAPTCHA_CHECK = "s_start_hcaptcha_check",
    r.START_TURNSTILE_CHECK = "s_start_turnstile_check",
    r.REDIRECTION_CANCELED = "s_redirection_canceled",
    r.RECAPTCHA_OKAY = "s_recaptcha_okay",
    r.HCAPTCHA_OKAY = "s_hcaptcha_okay",
    r.LINK_NOT_FOUND = "s_link_not_found",
    r.PROXY_DETECTED = "s_proxy_detected",
    r.WORKINK_PASS_LEFT = "s_workink_pass_left",
    r.PONG = "s_pong",
 */

export type Packet =
	// Client packets
	| { type: "c_social_started"; payload: { url: string } }
	| {
			type: "c_recaptcha_response"
			payload: {
				recaptchaResponse: string
			}
	  }
	| {
			type: "c_turnstile_response"
			payload: {
				token: string
			}
	  }
	| { type: "c_workink_pass_use"; payload: EmptyObject }
	| { type: "c_ping"; payload: { timestamp: number } }
	// Server packets
	| {
			type: "s_link_info"
			payload: {
				id: number
				isAdblockEnabled: boolean
				isDesktopOnly: boolean
				monetizationLevel: string
				monetizations: number[]
				monetizationsNeeded: number
				onlyStaticMonetizations: boolean
				socials: { type: string; url: string }[]
				userId: number
			}
	  }
	| { type: "s_social_done"; payload: { url: string } }
	| { type: "s_social_running"; payload: { time: number; url: string } }
	| {
			type: "s_link_destination"
			payload: {
				url: string
				usingPass: boolean
				usingPremium: boolean
			}
	  }
	| { type: "s_pong"; payload: { timestamp: number } }

export function decryptPacket(tokens: EncryptionTokens, packetData: string): Packet {
	let packet
	let l = ""
	const a = tokens[0].split("").map(p => p.charCodeAt(0)),
		r = packetData.substring(0, 2)
	let d = parseInt(r, 16)
	const o = packetData.substring(2).match(/.{1,2}/g) || []
	for (let p = 0; p < o.length; p++) {
		const m = parseInt(o[p], 16),
			L = a[(p * 2 + d) % a.length],
			j = (m - (p % 8) + 256) % 256,
			R = String.fromCharCode(j ^ L)
		;(l += R), (d = (d * 19 + 29) % 256)
	}
	packet = JSON.parse(l)
	let f = ""
	const b = tokens[1].split("").map(p => p.charCodeAt(0)),
		k = packet.payload.substring(0, 2)
	let g = parseInt(k, 16)
	const h = packet.payload.substring(2).match(/.{1,2}/g) || []
	for (let p = 0; p < h.length; p++) {
		const m = parseInt(h[p], 16),
			L = b[(p * 2 + g) % b.length],
			j = (m - (p % 8) + 256) % 256,
			R = String.fromCharCode(j ^ L)
		;(f += R), (g = (g * 19 + 29) % 256)
	}
	packet.payload = JSON.parse(f)
	return packet
}

export function encryptPacket(tokens: EncryptionTokens, packet: Packet): string {
	function encrypt(data: string, token: string) {
		let g = ""
		const D = token.split("").map(m => m.charCodeAt(0))
		let M = 1 % 256 // Doesn't need to be actual date it can be anything
		g += M.toString(16).padStart(2, "0")
		for (let m = 0; m < data.length; m++) {
			const b = data.charCodeAt(m),
				C = D[(m * 2 + M) % D.length],
				P = ((b ^ C) + (m % 8)) % 256
			;(g += P.toString(16).padStart(2, "0")), (M = (M * 19 + 29) % 256)
		}
		return g
	}

	const encryptedPayload = encrypt(JSON.stringify(packet.payload), tokens[1])

	const packetString = JSON.stringify({
		type: packet.type,
		payload: encryptedPayload,
	})

	const encryptedPacket = encrypt(packetString, tokens[0])

	return encryptedPacket
}

export async function getEncryptionTokens(
	browser: Browser,
	url: string
): Promise<EncryptionTokens | BypassError> {
	const page = await browser.newPage()

	const payload = `
    window.lmao=this
    `

	const client = await page.createCDPSession()
	const interceptManager = new RequestInterceptionManager(client)
	await interceptManager.intercept({
		urlPattern: `https://work.ink/_app/immutable/nodes/*`,
		modifyResponse({ body }) {
			if (!body) return
			body = body.replace(
				/(class .+{constructor\(.+,.+\){)(.+\(this,.+,window.location)/,
				`$1;${payload};$2`
			)
			return { body }
		},
	})

	await page.goto(url)

	while (true) {
		log("Waiting for cloudflare protection")
		await page.waitForNavigation()
		await sleep(1000)
		if (!(await page.$(".challenge-wrapper"))) break
	}

	log("Cloudflare protection passed")

	const encryptionTokensHandle = await page.waitForFunction(() => {
		//@ts-expect-error
		if (window.lmao && window.lmao.encryptionTokenOne && window.lmao.encryptionTokenOne) {
			//@ts-expect-error
			return [window.lmao.encryptionTokenOne, window.lmao.encryptionTokenTwo] as EncryptionTokens
		}
		return false
	})
	const encryptionTokens = await encryptionTokensHandle.jsonValue()

	if (!encryptionTokens) throw new BypassError("Error code 1")

	log("Encryption tokens found", encryptionTokens)

	return encryptionTokens
}

let encryptionTokens: EncryptionTokens

export async function createWorkInkBypassHandler(
	browser: undefined
): Promise<expressWs.WebsocketRequestHandler> {
	if (!encryptionTokens) {
		encryptionTokens = [
			"1zBsNKSRUbBrndQuFvrRkpcHmWXhmVDpW4zwbLQ61bMxTWJYWePm7tzbVwCL5kBo",
			"PDK51bIO7JQ3XyMDHBqKOCXJ4rSjsNGhEeKsW4rvJ4iGF8fTjpntt2rW88XkjobX",
		]
	}

	let packetQueue: { packet: Packet; delay: number }[] = []
	function queuePacket(packet: Packet, delay = 0) {
		packetQueue.push({ packet, delay })
	}

	/* 
    This receives every packet (client and server) and sends packets to the server
    */
	return (ws, req) => {
		function sendPacket(packet: Packet) {
			if (packet.type.startsWith("c")) {
				ws.send("»" + encryptPacket(encryptionTokens, packet))
			} else if (packet.type.startsWith("s")) {
				ws.send("«" + encryptPacket(encryptionTokens, packet))
			} else {
				throw new Error("Invalid packet type " + packet.type)
			}
		}
		function sendUrl(url: string) {
			ws.send("¿" + url)
		}

		ws.on("message", async message => {
			const packet = decryptPacket(encryptionTokens, message.toString())

			let socials: {
				type: string
				url: string
			}[] = []

			switch (packet.type) {
				case "c_recaptcha_response": {
					sendUrl("") // Refresh the page
					break
				}
				case "c_turnstile_response": {
					// Wait for captcha to be solved before sending skip
					log("Sending skip")
					queuePacket(
						{
							type: "c_workink_pass_use",
							payload: {},
						},
						100
					)

					break
				}
				case "s_link_info": {
					socials = packet.payload.socials
					const social = socials.pop()
					if (social) {
						/* queuePacket({
							type: "c_social_started",
							payload: { url: social.url },
						}) */
					}
					break
				}
				case "s_social_done": {
					const social = socials.pop()
					if (social) {
						/* queuePacket({
							type: "c_social_started",
							payload: { url: social.url },
						}) */
					}
					break
				}
				case "s_link_destination": {
					console.log("gottem", packet.payload.url)
					sendUrl(packet.payload.url)
					return
				}
			}

			if (packet.type !== "c_ping" && packet.type !== "s_pong") console.log(packet.type, packet.payload)
			sendPacket(packet)

			let queueItem = packetQueue.shift()
			if (queueItem) console.log("processing queued packets")
			while (queueItem) {
				if (queueItem.delay) {
					console.log("waiting", queueItem.delay, "for", queueItem.packet.type)
					await sleep(queueItem.delay)
				}
				console.log("sending", queueItem.packet.type, queueItem.packet.payload)
				sendPacket(queueItem.packet)

				queueItem = packetQueue.shift()
			}
		})
	}
}
