import { sql } from "drizzle-orm"
import { int, sqliteTable as table, text } from "drizzle-orm/sqlite-core"

export const users = table("users", {
	key: text().primaryKey(),
	/**
	 * How many credits the user has (null = unlimited)
	 */
	credits: int(),
	/**
	 * When the key expires (null = never)
	 */
	expiration: int({ mode: "timestamp" }),
	createdAt: int({ mode: "timestamp" }).default(sql`(unixepoch())`),
	firstUsed: int({ mode: "timestamp" }),
	lastUsed: int({ mode: "timestamp" }),
	lastUsedReason: text(),
})
