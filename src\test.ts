import * as acorn from "acorn"
import * as walk from "acorn-walk"
import { readFileSync } from "node:fs"

let src = readFileSync("./workInkReversing/a.js", "utf8")
let ast = acorn.parse(src, {
	ecmaVersion: "latest",
	sourceType: "module",
})

console.time("walk")

let clientPackets: Record<string, string> = {}
let serverPackets: Record<string, string> = {}

walk.simple(ast, {
	AssignmentExpression(node) {
		// 1. We only care if the right side is a String Literal
		if (node.right.type !== "Literal" || typeof node.right.value !== "string") {
			return
		}

		// 2. We only care if the left side is a MemberExpression (e.g., a.NAME)
		if (node.left.type !== "MemberExpression") {
			return
		}

		const value = node.right.value

		// 3. Heuristic: Packet values in your example start with 'c_' or 's_'
		// Adjust this regex if other prefixes exist.
		if (!/^[cs]_/.test(value)) {
			return
		}

		// 4. Extract the property name (the packet key)
		let key = ""

		if (node.left.property.type === "Identifier") {
			// Case: a.ANNOUNCE = ...
			key = node.left.property.name
		} else if (node.left.property.type === "Literal") {
			// Case: a["ANNOUNCE"] = ... (sometimes happens in minification)
			key = node.left.property.value as string
		}

		if (value.startsWith("c_")) {
			clientPackets[key] = value
		} else {
			serverPackets[key] = value
		}
	},
})
console.log(clientPackets)
console.log(serverPackets)
console.timeEnd("walk")
