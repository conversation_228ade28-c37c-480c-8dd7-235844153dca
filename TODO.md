* [ ] Change encryption token getting to only trigger at start and when there's a failed encryption/decryption (with cooldown)
* [ ] Add zod to config
* [ ] https://www.npmjs.com/package/rotating-file-stream

Make swift bypass show checkpoint

Vulcano bypass

Add timer to overlay work.ink bypass

Luarmor bypass

platoboost

Show captcha in overlay work.ink

============================

make work.ink faster by saving cookies to skip the checking browser page

use waitForFunction instead of waitForNavigation and hoping it works

**CodeRabbit**

**Avoid subclassing built-in `MessageEvent` for tagging; use a symbol flag.**

DOM event subclassing has edge-case compatibility; a symbol is simpler and safer.

**-class MessageEvent2 extends MessageEvent `<any>` {}**
+const BYPASS_TAG = Symbol("workInkBypass")
@@

- original.dispatchEvent(new MessageEvent2("message", { data: data.slice("toClient".length) }))

+ const ev = new MessageEvent("message", { data: data.slice("toClient".length) }) as MessageEvent & { [BYPASS_TAG]?: true }
+ ;(ev as any)[BYPASS_TAG] = true
+ original.dispatchEvent(ev)

And update the check:

**-    if (event instanceof MessageEvent2) return**

+ if ((event as any)[BYPASS_TAG]) return

Also applies to: 36-36

**CodeRabbit**

**Forward subprotocols to the proxy and close the proxy when the original closes.**

Keeps subprotocol negotiation consistent and avoids leaking the proxy socket.

**-  const proxy = new OriginalWebSocket(wsURL)**

+ const proxy = new OriginalWebSocket(wsURL, protocols)
+ original.addEventListener("close", () => { try { proxy.close(); } catch {} })
