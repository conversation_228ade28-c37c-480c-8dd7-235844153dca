html {
    width: 400px;
}

body {
    margin: 0;
    padding: 0;
    font-family: "Arial", "Helvetica", sans-serif;
}

header {
    background: #00a787;
    display: flex;
    color: white;
    padding: 10px;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}

header > img {
    height: 64px;
    align-self: center;
    margin-left: auto;
}

header > h1 {
    font-size: 24px;
    align-self: center;
    margin-left: 8px;
}

header > sub {
    align-self: flex-end;
    margin-bottom: 22px;
    margin-left: 6px;
    opacity: 0.5;
    font-size: 10px;
    margin-right: auto;
}

.passes-left {
    background: #eee;
    margin: 24px 16px;
    padding: 16px;
    border-radius: 12px;
    display: flex;
}

.passes-left > .title {
    font-size: 20px;
    font-weight: bold;
    text-transform: uppercase;
    align-self: center;
    margin-left: 10px;
}

.passes-left > .pass-count {
    margin-left: auto;
    align-self: center;
    background: #00a787;
    padding: 10px;
    border-radius: 100px;
    font-size: 20px;
    font-weight: bolder;
    font-family: 'Courier New', Courier, monospace;
    padding-top: 12px;
    color: white;
    width: 40px;
    text-align: center;
    margin-right: 10px;
}

.passes-left > .pass-count > img {
    margin-bottom: -3px;
    transform: scale(1.5);
}

.explaination {
    margin: 24px 32px;
}

.explaination > h2 {
    margin: 24px 0 16px 0;
    padding: 0;
    font-size: 20px;
}

.explaination > p {
    margin: 0;
    padding: 0;
    font-size: 16px;
    text-align: justify;
}

.explaination > ul {
    padding: 0;
    padding-left: 16px;
    font-size: 16px;
    margin-bottom: 24px;
}

.explaination > ul > li {
    margin-bottom: 4px;
}

footer {
    background: #eee;
    padding: 16px;
    font-size: 12px;
    color: #ababab;
    text-align: center;
    border-top-right-radius: 10px;
    border-top-left-radius: 10px;
}

footer > a {
    color: #ababab;
}