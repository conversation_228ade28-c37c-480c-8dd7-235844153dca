/**
 * The `unsafeWindow` object provides access to the `window` object of the page that <PERSON><PERSON><PERSON><PERSON> is running on, rather than the `window` object of the Tampermonkey extension. This can be useful in some cases, such as when a userscript needs to access a JavaScript library or variable that is defined on the page.
 *
 * Requires the `unsafeWindow` grant.
 */
declare const unsafeWindow: Window & typeof globalThis

type IdkValue = object | string | number | boolean | null | undefined

declare const GM: {
	/**
	 * Sets a value in the GM's storage asynchronously.
	 *
	 * Requires the `GM.setValue` grant.
	 */
	setValue(key: string, value: IdkValue): Promise<void>
	/**
	 * Gets a value from the userscript's storage asynchronously.
	 *
	 * Requires the `GM.getValue` grant.
	 */
	getValue(key: string, defaultValue?: any): Promise<any>
	/**
	 * Deletes a value from storage asynchronously.
	 *
	 * Requires the `GM.deleteValue` grant.
	 */
	deleteValue(key: string): Promise<void>
	/**
	 * List all values in storage asynchronously.
	 *
	 * Requires the `GM.listValues` grant.
	 */
	listValues(): Promise<string[]>
	/**
	 * Set multiple values in storage at once asynchronously.
	 *
	 * Requires the `GM.setValues` grant.
	 */
	setValues(values: { [key: string]: IdkValue }): Promise<void>
	/**
	 * Get multiple values from storage at once asynchronously.
	 *
	 * Requires the `GM.getValues` grant.
	 */
	getValues(keysOrDefaults: string[] | { [key: string]: any }): Promise<{ [key: string]: any }>
	/**
	 * Delete multiple values from storage at once asynchronously.
	 *
	 * Requires the `GM.deleteValues` grant.
	 */
	deleteValues(keys: string[]): Promise<void>
	/**
	 * Add a change listener to a storage value asynchronously.
	 *
	 * Requires the `GM.addValueChangeListener` grant.
	 */
	addValueChangeListener(
		key: string,
		callback: (key: string, oldValue: any, newValue: any, remote: boolean) => void
	): Promise<string>
	/**
	 * Remove a storage change listener asynchronously.
	 *
	 * Requires the `GM.removeValueChangeListener` grant.
	 */
	removeValueChangeListener(listenerId: string): Promise<void>
	/**
	 * Make an XMLHttpRequest asynchronously.
	 *
	 * Requires the `GM.xmlhttpRequest` grant.
	 */
	xmlHttpRequest(details: Parameters<typeof GM_xmlhttpRequest>[0]): Promise<{
		finalUrl: string
		readyState: number
		status: number
		statusText: string
		responseHeaders: string
		response?: any
		responseXML?: Document
		responseText: string
	}>
	/**
	 * Get information about the script and Tampermonkey.
	 *
	 * Requires no grant.
	 */
	info: GMInfo
	/**
	 * Adds a HTML element to the page asynchronously.
	 *
	 * Requires the `GM.addElement` grant.
	 */
	addElement: typeof GM_addElement
	/**
	 * Add CSS styles to the document asynchronously.
	 *
	 * Requires the `GM.addStyle` grant.
	 */
	addStyle(css: string): Promise<HTMLStyleElement>
	/**
	 * Get the text content of a resource asynchronously.
	 *
	 * Requires the `GM.getResourceText` grant.
	 */
	getResourceText(name: string): Promise<string>
	/**
	 * Get the URL of a resource asynchronously.
	 *
	 * Requires the `GM.getResourceUrl` grant.
	 */
	getResourceUrl(name: string): Promise<string>
	/**
	 * Log a message to the console asynchronously.
	 *
	 * Requires the `GM.log` grant.
	 */
	log(message: string): Promise<void>
	/**
	 * Show a notification asynchronously.
	 *
	 * Requires the `GM.notification` grant.
	 */
	notification(details: Parameters<typeof GM_notification>[0]): Promise<boolean>
	/**
	 * Opens a URL in a new tab asynchronously.
	 *
	 * Requires the `GM.openInTab` grant.
	 */
	openInTab: typeof GM_openInTab
	/**
	 * Register a menu command asynchronously.
	 *
	 * Requires the `GM.registerMenuCommand` grant.
	 */
	registerMenuCommand: typeof GM_registerMenuCommand
	/**
	 * Unregister a menu command asynchronously.
	 *
	 * Requires the `GM.unregisterMenuCommand` grant.
	 */
	unregisterMenuCommand(menuCmdId: number): Promise<void>
	/**
	 * Copy data to the clipboard asynchronously.
	 *
	 * Requires the `GM.setClipboard` grant.
	 */
	setClipboard(data: string, info?: string | { type?: string; mimetype?: string }): Promise<void>
	/**
	 * Get tab-specific data asynchronously.
	 *
	 * Requires the `GM.getTab` grant.
	 */
	getTab(): Promise<object>
	/**
	 * Save tab-specific data asynchronously.
	 *
	 * Requires the `GM.saveTab` grant.
	 */
	saveTab(tab: object): Promise<void>
	/**
	 * Get all tab data as object asynchronously.
	 *
	 * Requires the `GM.getTabs` grant.
	 */
	getTabs(): Promise<{ [key: string]: any }>
	cookie: {
		list(
			details: Parameters<typeof GM_cookie.list>[0]
		): Promise<Parameters<NonNullable<Parameters<typeof GM_cookie.list>[1]>>[0]>
		set(details: Parameters<typeof GM_cookie.set>[0]): Promise<void>
		delete(details: Parameters<typeof GM_cookie.delete>[0]): Promise<void>
	}
	/**
	 * Downloads a URL to the local disk asynchronously.
	 *
	 * Requires the `GM.download` grant.
	 */
	download(details: {
		url: string | Blob | File
		name: string
		headers?: { [key: string]: string }
		saveAs?: boolean
		conflictAction?: "uniquify" | "overwrite" | "prompt"
	}): Promise<void>
	download(url: string, name: string): Promise<void>
}

/**
 * Adds an HTML element to the document.
 *
 * Requires the `GM_addElement` grant.
 *
 * @param tagName The name of the HTML tag to create
 * @param attributes Object containing the attributes to set on the element
 */
declare function GM_addElement(tagName: string, attributes: { [key: string]: any }): HTMLElement
declare function GM_addElement(
	parentNode: HTMLElement,
	tagName: string,
	attributes: { [key: string]: any }
): HTMLElement

/**
 * Adds CSS styles to the document.
 *
 * Requires the `GM_addStyle` grant.
 *
 * @param css The CSS code to add
 */
declare function GM_addStyle(css: string): HTMLStyleElement

/**
 * Downloads a URL to the local disk.
 *
 * Requires the `GM_download` grant.
 */
declare function GM_download(details: {
	url: string | Blob | File
	name: string
	headers?: { [key: string]: string }
	saveAs?: boolean
	conflictAction?: "uniquify" | "overwrite" | "prompt"
	onload?: () => void
	onerror?: (download: { error: string; details?: string }) => void
	onprogress?: () => void
	ontimeout?: () => void
}): { abort: () => void }
declare function GM_download(url: string, name: string): void

/**
 * Get the text content of a resource defined in the metadata block.
 *
 * Requires the `GM_getResourceText` grant.
 */
declare function GM_getResourceText(name: string): string

/**
 * Get the URL of a resource defined in the metadata block.
 *
 * Requires the `GM_getResourceURL` grant.
 */
declare function GM_getResourceURL(name: string): string

/**
 * Get information about the script and Tampermonkey.
 *
 * Requires no grant.
 */
declare const GM_info: GMInfo

/**
 * Log a message to the console.
 *
 * Requires the `GM_log` grant.
 */
declare function GM_log(message: string): void

/**
 * Show a notification.
 *
 * Requires the `GM_notification` grant.
 */
declare function GM_notification(details: {
	text: string
	title?: string
	image?: string
	highlight?: boolean
	silent?: boolean
	timeout?: number
	onclick?: () => void
	ondone?: () => void
	tag?: string
	url?: string
}): void
declare function GM_notification(text: string, title: string, image?: string, onclick?: () => void): void

/**
 * Opens a URL in a new tab.
 *
 * Requires the `GM_openInTab` grant.
 */
declare function GM_openInTab(
	url: string,
	options?: {
		active?: boolean
		insert?: boolean
		setParent?: boolean
		incognito?: boolean
		loadInBackground?: boolean
	}
): { close: () => void; onclose: () => void; closed: boolean }
declare function GM_openInTab(
	url: string,
	loadInBackground?: boolean
): { close: () => void; onclose: () => void; closed: boolean }

/**
 * Register a menu command that appears in Tampermonkey's menu.
 *
 * Requires the `GM_registerMenuCommand` grant.
 */
declare function GM_registerMenuCommand(
	name: string,
	callback: (event: MouseEvent | KeyboardEvent) => void,
	options?: {
		id?: number | string
		accessKey?: string
		autoClose?: boolean
		title?: string
	}
): number
declare function GM_registerMenuCommand(name: string, callback: () => void, accessKey?: string): number

/**
 * Unregister a menu command.
 *
 * Requires the `GM_unregisterMenuCommand` grant.
 */
declare function GM_unregisterMenuCommand(menuCmdId: number): void

/**
 * Copy data to the clipboard.
 *
 * Requires the `GM_setClipboard` grant.
 */
declare function GM_setClipboard(
	data: string,
	info?: string | { type?: string; mimetype?: string },
	callback?: () => void
): void

/**
 * Get tab-specific data that persists as long as the tab is open.
 *
 * Requires the `GM_getTab` grant.
 */
declare function GM_getTab(callback: (tab: object) => void): void

/**
 * Save tab-specific data.
 *
 * Requires the `GM_saveTab` grant.
 */
declare function GM_saveTab(tab: object, callback?: () => void): void

/**
 * Get all tab data as object.
 *
 * Requires the `GM_getTabs` grant.
 */
declare function GM_getTabs(callback: (tabs: { [key: string]: any }) => void): void

/**
 * Sets a value in the userscript's storage.
 *
 * Requires the `GM_setValue` grant.
 */
declare function GM_setValue(key: string, value: IdkValue): void

/**
 * Gets a value from the userscript's storage.
 *
 * Requires the `GM_getValue` grant.
 */
declare function GM_getValue(key: string, defaultValue?: any): any

/**
 * Deletes a value from storage.
 *
 * Requires the `GM_deleteValue` grant.
 */
declare function GM_deleteValue(key: string): void

/**
 * List all values in storage.
 *
 * Requires the `GM_listValues` grant.
 */
declare function GM_listValues(): string[]

/**
 * Set multiple values in storage at once.
 *
 * Requires the `GM_setValues` grant.
 */
declare function GM_setValues(values: { [key: string]: IdkValue }): void

/**
 * Get multiple values from storage at once.
 *
 * Requires the `GM_getValues` grant.
 */
declare function GM_getValues(keysOrDefaults: string[] | { [key: string]: any }): { [key: string]: any }

/**
 * Delete multiple values from storage at once.
 *
 * Requires the `GM_deleteValues` grant.
 */
declare function GM_deleteValues(keys: string[]): void

/**
 * Add a change listener to a storage value.
 *
 * Requires the `GM_addValueChangeListener` grant.
 */
declare function GM_addValueChangeListener(
	key: string,
	callback: (key: string, oldValue: any, newValue: any, remote: boolean) => void
): string

/**
 * Remove a storage change listener.
 *
 * Requires the `GM_removeValueChangeListener` grant.
 */
declare function GM_removeValueChangeListener(listenerId: string): void

/**
 * Make an XMLHttpRequest.
 *
 * Requires the `GM_xmlhttpRequest` grant.
 */
declare function GM_xmlhttpRequest(details: {
	method?: string
	url: string | URL | File | Blob
	headers?: { [key: string]: string }
	data?: string | Blob | File | object | any[] | FormData | URLSearchParams
	cookie?: string
	binary?: boolean
	nocache?: boolean
	revalidate?: boolean
	timeout?: number
	context?: any
	responseType?: "arraybuffer" | "blob" | "json" | "stream"
	overrideMimeType?: string
	anonymous?: boolean
	fetch?: boolean
	user?: string
	password?: string
	onabort?: () => void
	onerror?: () => void
	onloadstart?: () => void
	onprogress?: () => void
	onreadystatechange?: () => void
	ontimeout?: () => void
	onload?: (response: {
		finalUrl: string
		readyState: number
		status: number
		statusText: string
		responseHeaders: string
		response?: any
		responseXML?: Document
		responseText: string
	}) => void
}): { abort: () => void }

/**
 * Register rules for web request manipulation.
 *
 * Requires the `GM_webRequest` grant.
 */
declare function GM_webRequest(
	rules: Array<{
		selector:
			| string
			| {
					include?: string | string[]
					match?: string | string[]
					exclude?: string | string[]
			  }
		action:
			| string
			| {
					cancel?: boolean
					redirect?:
						| {
								url: string
								from?: string
								to?: string
						  }
						| string
			  }
	}>,
	listener: (
		info: string,
		message: string,
		details: {
			rule: object
			url: string
			redirect_url?: string
			description?: string
		}
	) => void
): void

/**
 * Cookie management API.
 *
 * Requires the `GM_cookie` grant.
 */
declare const GM_cookie: {
	list(
		details: {
			url?: string
			domain?: string
			name?: string
			path?: string
			partitionKey?: object
			topLevelSite?: string
		},
		callback?: (
			cookies: Array<{
				domain: string
				expirationDate?: number
				firstPartyDomain?: string
				partitionKey?: object
				topLevelSite?: string
				hostOnly: boolean
				httpOnly: boolean
				name: string
				path: string
				sameSite: string
				secure: boolean
				session: boolean
				value: string
			}>,
			error?: string
		) => void
	): void

	set(
		details: {
			url?: string
			name: string
			value: string
			domain?: string
			firstPartyDomain?: string
			partitionKey?: object
			topLevelSite?: string
			path?: string
			secure?: boolean
			httpOnly?: boolean
			expirationDate?: number
		},
		callback?: (error?: string) => void
	): void

	delete(
		details: {
			url?: string
			name: string
			firstPartyDomain?: string
			partitionKey?: object
			topLevelSite?: string
		},
		callback?: (error?: string) => void
	): void
}

interface GMInfo {
	container?: {
		id: string
		name?: string
	}
	downloadMode: string
	isFirstPartyIsolation?: boolean
	isIncognito: boolean
	sandboxMode: "js" | "raw" | "dom"
	scriptHandler: string
	scriptMetaStr: string | null
	scriptUpdateURL: string | null
	scriptWillUpdate: boolean
	userAgentData: {
		brands?: Array<{
			brand: string
			version: string
		}>
		mobile?: boolean
		platform?: string
		architecture?: string
		bitness?: string
	}
	version?: string
	script: {
		antifeatures: { [antifeature: string]: { [locale: string]: string } }
		author: string | null
		blockers: string[]
		connects: string[]
		copyright: string | null
		deleted?: number
		description_i18n: { [locale: string]: string } | null
		description: string
		downloadURL: string | null
		excludes: string[]
		fileURL: string | null
		grant: string[]
		header: string | null
		homepage: string | null
		icon: string | null
		icon64: string | null
		includes: string[]
		lastModified: number
		matches: string[]
		name_i18n: { [locale: string]: string } | null
		name: string
		namespace: string | null
		position: number
		resources: Array<{
			name: string
			url: string
			error?: string
			content?: string
			meta?: string
		}>
		supportURL: string | null
		system?: boolean
		"run-at": string | null
		"run-in": string[] | null
		unwrap: boolean | null
		updateURL: string | null
		version: string
		webRequest: Array<{
			selector:
				| { include?: string | string[]; match?: string | string[]; exclude?: string | string[] }
				| string
			action:
				| string
				| {
						cancel?: boolean
						redirect?:
							| {
									url: string
									from?: string
									to?: string
							  }
							| string
				  }
		}> | null
		options: {
			check_for_updates: boolean
			comment: string | null
			compatopts_for_requires: boolean
			compat_wrappedjsobject: boolean
			compat_metadata: boolean
			compat_foreach: boolean
			compat_powerful_this: boolean | null
			sandbox: string | null
			noframes: boolean | null
			unwrap: boolean | null
			run_at: string | null
			run_in: string | null
			override: {
				use_includes: string[]
				orig_includes: string[]
				merge_includes: boolean
				use_matches: string[]
				orig_matches: string[]
				merge_matches: boolean
				use_excludes: string[]
				orig_excludes: string[]
				merge_excludes: boolean
				use_connects: string[]
				orig_connects: string[]
				merge_connects: boolean
				use_blockers: string[]
				orig_run_at: string | null
				orig_run_in: string[] | null
				orig_noframes: boolean | null
			}
		}
	}
}

declare interface Window {
	/**
	 * Event handler for URL changes in single-page applications.
	 *
	 * Requires the `window.onurlchange` grant.
	 */
	onurlchange: ((this: Window, ev: Event) => any) | null

	/**
	 * Close the current window/tab.
	 * Note: For security reasons it is not allowed to close the last tab of a window.
	 *
	 * Requires the `window.close` grant.
	 */
	close(): void

	/**
	 * Brings the window to the front.
	 *
	 * Requires the `window.focus` grant.
	 */
	focus(): void
}
