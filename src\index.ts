import cors from "cors"
import express from "express"
import expressWs from "express-ws"
import { rateLimit } from "express-rate-limit"
import morgan from "morgan"
import path from "node:path"
import { applyBypasses, BypassError } from "./bypasses/index.js"
import { fileURLToPath } from "node:url"
import { connect } from "puppeteer-real-browser"
import { db, schema } from "./db/index.js"
import { eq } from "drizzle-orm"
import fs from "node:fs"
import { config } from "./config.js"

export const _filename = fileURLToPath(import.meta.url)
export const _dirname = path.dirname(_filename)

// Create public key
if (!(await db.select().from(schema.users).where(eq(schema.users.key, "public"))).length) {
	await db.insert(schema.users).values({ key: "public" })
}

const { browser, page } = await connect({ turnstile: true })

const { app, getWss, applyTo } = expressWs(express())

var httpLogStream = fs.createWriteStream(config.httpLogFile, { flags: "a" })

app.use(
	morgan("combined", { stream: httpLogStream }),
	/* rateLimit({
		windowMs: 10 * 60 * 1000,
		limit: 10,
		standardHeaders: "draft-8", // draft-6: `RateLimit-*` headers; draft-7 & draft-8: combined `RateLimit` header
		legacyHeaders: false, // Disable the `X-RateLimit-*` headers.
		ipv6Subnet: 56, // Set to 60 or 64 to be less aggressive, or 52 or 48 to be more aggressive
		// store: ... , // Redis, Memcached, etc. See below.
	}), */
	cors({
		origin: "*",
		methods: ["GET"],
		allowedHeaders: ["x-api-key"],
	})
)

app.get("/", async (req, res) => {
	res.send("Why u here")
})

await applyBypasses(app, browser)

app.use((err: unknown, req: express.Request, res: express.Response, next: express.NextFunction) => {
	if (err instanceof BypassError) {
		res.send("Error: " + err.message)
	} else {
		console.error(err)
		res.send("Error: Internal server error")
	}
})

app.listen(config.port, () => {
	console.log(`Server started on port ${config.port}`)
})
