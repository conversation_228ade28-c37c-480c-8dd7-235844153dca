import fs from "node:fs"
import { config } from "./config.js"
import color from "yoctocolors"
import stripAnsi from "strip-ansi"
import { _dirname } from "./index.js"
import path from "node:path"

export type LogLevel = "debug" | "log" | "info" | "warn" | "error"

/* export default class Logger {
	_scope

	constructor(scope: string = "") {
		this._scope = scope
	}

	scope(name: string) {
		return new Logger(this._scope ? `${this._scope}:${name}` : name)
	}

	logWithLevel(level: LogLevel, ...args: any[]) {
		const logLevels: Record<LogLevel, number> = {
			debug: 0,
			log: 1,
			info: 2,
			warn: 3,
			error: 4,
		}

		if (logLevels[level] < logLevels[config.logLevel]) return

		const colorMap: Record<LogLevel, color.Format> = {
			debug: color.bgBlack,
			log: color.white,
			info: color.cyan,
			warn: color.yellow,
			error: color.red,
		}

		const header = colorMap[level](
			`${new Date().toISOString()} ${level.toUpperCase()}` +
				(this._scope ? " " + color.magenta(this._scope) : "")
		)
		let message = `${header} ${[...args].filter(arg => !(arg instanceof Error)).join(" ")}`

		// Add stack traces at the end
		for (const arg of args) {
			if (arg instanceof Error && arg.stack) {
				message += "\n" + arg.stack
			}
		}

		console.log(message)
		fs.appendFileSync(config.logFile, stripAnsi(message) + "\n")
	}

	log(...args: any[]) {
		this.logWithLevel("log", ...args)
	}

	info(...args: any[]) {
		this.logWithLevel("info", ...args)
	}

	warn(...args: any[]) {
		this.logWithLevel("warn", ...args)
	}

	error(...args: any[]) {
		this.logWithLevel("error", ...args)
	}

	debug(...args: any[]) {
		this.logWithLevel("debug", ...args)
	}
} */

const logger = {
	getCallerScope(offset = 0) {
		try {
			// Throw an error to capture the stack
			throw new Error()
		} catch (err) {
			const stack = (err as Error).stack?.split("\n")

			if (!stack) throw new Error("Stack not found")

			const line = stack![2 + offset]

			if (!line) {
				console.log(stack)
				throw new Error("Caller not found in stack")
			}

			const match = line.match(/\s*at (.+?) \(file:\/\/\/(.+):(\d+?:\d+?)\)/)

			if (!match) {
				console.log(stack)
				throw new Error("Could not parse stack")
			}

			const functionName = match[1]
			const filePath = path.relative(_dirname, match[2])
			const lineNumbers = match[3]

			console.log(functionName)
			console.log(filePath)
			console.log(lineNumbers)

			console.log(`${filePath}:${functionName}:${lineNumbers}`.replaceAll("\\", "/"))
		}
	},
}

export default logger

export function logTest(message: string) {
	try {
		// Throw an error to capture the stack
		throw new Error()
	} catch (err) {
		const stack = (err as Error).stack?.split("\n")
		const line = stack![2]

		const match = line.match(/\s*at (.+?) \(file:\/\/\/(.+):(\d+?:\d+?)\)/)

		const functionName = match![1]
		const filePath = path.relative(_dirname, match![2])
		const lineNumbers = match![3]

		console.log(functionName)
		console.log(filePath)
		console.log(lineNumbers)

		console.log(`${filePath}:${functionName}:${lineNumbers}`.replaceAll("\\", "/"))
	}
}
