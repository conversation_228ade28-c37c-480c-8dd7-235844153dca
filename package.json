{"name": "server", "version": "1.0.0", "description": "", "type": "module", "main": "prod/index.js", "scripts": {"start": "node .", "start:watch": "nodemon -w prod", "build": "tsc", "build:watch": "tsc --watch", "test": "node prod/test.js", "test:watch": "nodemon prod/test.js -w prod", "dev": "pnpm run build && pnpm run start", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@9.15.3+sha512.1f79bc245a66eb0b07c5d4d83131240774642caaa86ef7d0434ab47c0d16f66b04e21e0c086eb61e62c77efc4d7f7ec071afad3796af64892fae66509173893a", "dependencies": {"@libsql/client": "^0.15.15", "acorn": "^8.15.0", "acorn-walk": "^8.3.4", "cors": "^2.8.5", "dotenv": "^17.2.2", "drizzle-orm": "^0.44.5", "express": "^5.1.0", "express-rate-limit": "^8.1.0", "express-ws": "^5.0.2", "js-confuser": "^2.0.0", "morgan": "^1.10.1", "puppeteer-intercept-and-modify-requests": "^1.3.1", "puppeteer-real-browser": "^1.4.4", "strip-ansi": "^7.1.2", "ws": "^8.18.3", "yoctocolors": "^2.1.2", "zod": "^4.1.11"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/express-ws": "^3.0.5", "@types/morgan": "^1.9.10", "@types/node": "^24.5.2", "@types/ws": "^8.18.1", "drizzle-kit": "^0.31.4", "nodemon": "^3.1.10", "typescript": "^5.9.2"}}