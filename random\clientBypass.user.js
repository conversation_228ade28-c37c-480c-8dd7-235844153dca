// ==UserScript==
// @name         Work Ink Bypass
// @namespace    http://tampermonkey.net/
// @version      1
// @description  Work Ink devs are dumb
// <AUTHOR>
// @match        https://work.ink/*
// @icon         data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==
// @run-at       document-start
// @grant        unsafeWindow
// ==/UserScript==

;(async function () {
	"use strict"

	// Config
	const Redirect = false
	const RedirectDelay = 0

	function decryptPacket(encryptionTokenOne, encryptionTokenTwo, packetData) {
		let packet
		let l = ""
		const a = encryptionTokenOne.split("").map(p => p.charCodeAt(0)),
			r = packetData.substring(0, 2)
		let d = parseInt(r, 16)
		const o = packetData.substring(2).match(/.{1,2}/g) || []
		for (let p = 0; p < o.length; p++) {
			const m = parseInt(o[p], 16),
				L = a[(p * 2 + d) % a.length],
				j = (m - (p % 8) + 256) % 256,
				R = String.fromCharCode(j ^ L)
			;(l += R), (d = (d * 19 + 29) % 256)
		}
		packet = JSON.parse(l)
		let f = ""
		const b = encryptionTokenTwo.split("").map(p => p.charCodeAt(0)),
			k = packet.payload.substring(0, 2)
		let g = parseInt(k, 16)
		const h = packet.payload.substring(2).match(/.{1,2}/g) || []
		for (let p = 0; p < h.length; p++) {
			const m = parseInt(h[p], 16),
				L = b[(p * 2 + g) % b.length],
				j = (m - (p % 8) + 256) % 256,
				R = String.fromCharCode(j ^ L)
			;(f += R), (g = (g * 19 + 29) % 256)
		}
		packet.payload = JSON.parse(f)
		return packet
	}

	function encryptPacket(encryptionTokenOne, encryptionTokenTwo, packet) {
		function encrypt(data, token) {
			let g = ""
			const D = token.split("").map(m => m.charCodeAt(0))
			let M = 1 % 256 // Doesn't need to be actual date it can be anything
			g += M.toString(16).padStart(2, "0")
			for (let m = 0; m < data.length; m++) {
				const b = data.charCodeAt(m),
					C = D[(m * 2 + M) % D.length],
					P = ((b ^ C) + (m % 8)) % 256
				;(g += P.toString(16).padStart(2, "0")), (M = (M * 19 + 29) % 256)
			}
			return g
		}

		const encryptedPayload = encrypt(JSON.stringify(packet.payload), encryptionTokenTwo)

		const packetString = JSON.stringify({
			type: packet.type,
			payload: encryptedPayload,
		})

		const encryptedPacket = encrypt(packetString, encryptionTokenOne)

		return encryptedPacket
	}

	async function sleep(ms) {
		return new Promise(resolve => setTimeout(resolve, ms))
	}

	let onSend = null
	let onReceive = null
	let ws = null

	// Hook websocket
	const OriginalWebSocket = unsafeWindow.WebSocket
    unsafeWindow.WebSocket = function (url, protocols) {
        // Only hook websockets to work.ink api
        if (!url.startsWith("wss://work.ink/_api/v2/ws")) return new OriginalWebSocket(url, protocols)
        
		ws = new OriginalWebSocket(url, protocols)

		const originalSend = ws.send
		ws.send = function (data) {
			if (onSend) onSend(data)
			originalSend.call(ws, data)
		}

		ws.addEventListener("message", event => {
			if (onReceive) onReceive(event.data)
		})

		return ws
	}

	// Wait for a reference to the script url
	while (!document.querySelector('link[href*="nodes/3."]')) {
		console.log("Waiting for reference to script url")
		await sleep(500)
	}
	const scriptUrl = document.querySelector('link[href*="nodes/3."]').href

	// Fetch the script
	const response = await fetch(scriptUrl)
	let code = await response.text()

	console.log("Original code", code)

	let encryptionGetter = null

	// Extract the encryption tokens
	code = code.replace(
		/[a-zA-Z0-9_$]+\(this,[a-zA-Z0-9_$]+,\([a-zA-Z0-9_$]+,[a-zA-Z0-9_$]+\)=>{(const [a-zA-Z0-9_$]+={.+?};let ([a-zA-Z0-9_$])=[a-zA-Z0-9_$]\[.+?\],([a-zA-Z0-9_$])=[a-zA-Z0-9_$]\[.+?\];)/,
		(substring, ...args) => {
            const magic = args[0]
			const varName1 = args[1]
			const varName2 = args[2]
			encryptionGetter =
				magic + `window.encryptionTokenOne=${varName1};window.encryptionTokenTwo=${varName2};`
			console.log("Encryption getter", encryptionGetter)
			return substring
		}
	)

	if (!encryptionGetter) {
		console.error("Could not make encryption getter")
		return
	}

	// Old
	/* // Hook function to extract data
	code = code.replace(/var (\w+)=\(\w+,\w+,\w+\)=>.+?;/, (substring, ...args) => {
		const oldName = args[0]
		const newName = oldName + "_original"

		console.log("Hooking", oldName)

		let replacement = substring.replace(oldName, newName)
		replacement += `function ${oldName}(object, property, value){if (property == "encryptionTokenOne" || property == "encryptionTokenTwo") window[property] = value;return ${newName}(object, property, value)};`

		return replacement
	}) */

	// Unfuck imports
	code = code.replaceAll('import"../', 'import "https://work.ink/_app/immutable/')
	code = code.replaceAll('from"../', 'from "https://work.ink/_app/immutable/')

	// Get the name of the class
	const className = code
		.match(/class \w+(?=.+?wss:\/\/)/g)
		.pop()
		.slice(6)

	console.log(
		"Injecting encryption getter at",
		new RegExp(`class ${className}{constructor\\([a-zA-Z0-9_$]+,[a-zA-Z0-9_$]+\\){`)
	)
	// Inject the encryption getter
	code = code.replace(
		new RegExp(`class ${className}{constructor\\([a-zA-Z0-9_$]+,[a-zA-Z0-9_$]+\\){`),
		(substring, ...args) => {
			console.log("Injected encryption getter")
			return substring + `;(()=>{${encryptionGetter}})();`
		}
	)

	// At the end create an instance of the class to trigger the constructor
    code += `;new ${className}(null,null);`

    console.log("Modified code", code)

	// Add the code to the page to run it
	const element = document.createElement("script")
	element.textContent = code
	element.type = "module"
    document.head.appendChild(element)
    console.log("Added script to page")

	// Wait for and get the encryption tokens
    while (!unsafeWindow.encryptionTokenOne || !unsafeWindow.encryptionTokenTwo) {
        console.log("Waiting for encryption tokens")
		await sleep(500)
	}
	const encryptionTokenOne = unsafeWindow.encryptionTokenOne
	const encryptionTokenTwo = unsafeWindow.encryptionTokenTwo

	console.log("Got encryption tokens", encryptionTokenOne, encryptionTokenTwo)

	// Handle outgoing packets
	onSend = async data => {
		const packet = decryptPacket(encryptionTokenOne, encryptionTokenTwo, data)
		console.log("Sent", packet)

		switch (packet.type) {
			case "c_turnstile_response": {
				const passUsePacket = {
					type: "c_workink_pass_use",
					payload: {},
				}

				console.log("Turnstile finished sending pass use", packet)
				if (!ws) console.error("How did we capture a websocket packet without a websocket?")

				await sleep(500)

				ws.send(encryptPacket(encryptionTokenOne, encryptionTokenTwo, passUsePacket))

				break
			}
		}
	}

	// Handle incoming packets
	onReceive = async data => {
		const packet = decryptPacket(encryptionTokenOne, encryptionTokenTwo, data)
		console.log("Received", packet)

		switch (packet.type) {
			case "s_link_destination": {
				console.log("Got destination", packet.payload.url)
				if (Redirect) {
					await sleep(RedirectDelay)
					window.location = packet.payload.url
				}
				break
			}
		}
	}
})()
