;(async () => {
	console.log("Starting magic")
	async function sleep(ms: number) {
		return new Promise(resolve => setTimeout(resolve, ms))
	}

	function findTextNode(text: string) {
		let walker = document.createTreeWalker(document.body, NodeFilter.SHOW_TEXT, {
			acceptNode: function (node) {
				if (node.nodeValue && node.nodeValue.trim() === text) {
					return NodeFilter.FILTER_ACCEPT
				}
				return NodeFilter.FILTER_SKIP
			},
		})

		return walker.nextNode()
	}

	function addStyle(style: string) {
		document.head.appendChild(document.createElement("style")).textContent = style
	}

	const script = document.currentScript as HTMLScriptElement
	const host = script.getAttribute("host")!

	switch (location.hostname) {
		case "work.ink": {
			/* addStyle(`
                #overlay {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100vw;
                    height: 100vh;
                    background-color: rgba(0, 0, 0, 0.75);
                    z-index: 99999;
                    pointer-events: none;
                }
                #overlay > * {
                    pointer-events: auto;
                }
            `)

			const overlay = document.createElement("div")
			overlay.id = "overlay"

			document.documentElement.appendChild(overlay)
			console.log(overlay) */

			class MessageEvent2 extends MessageEvent<any> {}

			const wsURL = new URL(host)
			wsURL.protocol = "ws"
			wsURL.pathname = "/workInk"
			wsURL.searchParams.set("x-api-key", "public")

			const OriginalWebSocket = window.WebSocket
			// @ts-expect-error
			window.WebSocket = function (url: string | URL, protocols?: string | string[] | undefined) {
				console.log("Creating websocket", url)
				if (!url.toString().includes("wss://work.ink/_api/v2/ws"))
					return new OriginalWebSocket(url, protocols)

				const original = new OriginalWebSocket(url, protocols)
				const proxy = new OriginalWebSocket(wsURL)

				const originalSend = original.send.bind(original)

				original.send = function (data) {
					proxy.send(data)
				}

				original.addEventListener("message", function (event) {
					if (event instanceof MessageEvent2) return
					proxy.send(event.data)
					event.stopImmediatePropagation()
				})

				proxy.addEventListener("message", async function (event) {
					const data = event.data
					if (typeof data !== "string") throw 1
					if (data === "") throw 2

					if (data.startsWith("toServer")) {
						originalSend(data.slice(8))
					} else if (data.startsWith("toClient")) {
						original.dispatchEvent(new MessageEvent2("message", { data: data.slice(8) }))
					} else if (data.startsWith("url")) {
						// TODO
					} else if (data.startsWith("Error: ")) {
						console.error("Error received: ", data.slice(7))
					}
				})
				return original
			}

			// Preserve prototype and constants
			window.WebSocket.prototype = OriginalWebSocket.prototype
			Object.keys(OriginalWebSocket).forEach(key => {
				// @ts-expect-error
				window.WebSocket[key] = OriginalWebSocket[key]
			})

			break
		}
		case "swiftkeysystem.vip": {
			function checkTurnstile() {
				const turnstile = document.querySelector("[name=cf-turnstile-response]")
				// @ts-expect-error
				if (turnstile && turnstile.value !== "") {
					return true
				}
				return false
			}
			function checkHCaptcha() {
				const hcaptcha = document.querySelector("[data-hcaptcha-response]")
				if (hcaptcha && hcaptcha.getAttribute("data-hcaptcha-response") !== "") {
					return true
				}
				return false
			}
			// Only show captcha area
			addStyle(`
        .checkpoint-container {
            background: transparent !important;
            border: none !important;
            box-shadow: none !important;
        }
        .header, .instruction-area, .continue-button, .footer {
            display: none !important;
        }
        `)
			while (!checkTurnstile() && !checkHCaptcha()) {
				console.log("captcha not solved, waiting")
				await sleep(50)
			}
			console.log("captcha solved, continuing")
			// @ts-expect-error
			document.querySelector(".continue-button").click() // Click the continue button

			break
		}
		case "loot-link.com": {
			setTimeout(async () => {
				while (!findTextNode("UNLOCK CONTENT")) await sleep(100)
				findTextNode("UNLOCK CONTENT")!.textContent = "Just wait a lil"
			})

			// @ts-expect-error
			window.open = function () {}

			while (!document.querySelector(".btn-shadow")) await sleep(100)
			// @ts-expect-error
			document.querySelectorAll(".btn-shadow").forEach(b => b.click())

			while (!document.querySelector(".is-success")) await sleep(100)
			// @ts-expect-error
			document.querySelector(".is-success").click()

			break
		}
		case "auth.platorelay.com": {
			function run() {
				const button = document.querySelector("button")
				if (button) {
					button.disabled = false
					button.textContent = "Continue"
				}
				requestAnimationFrame(run)
			}
			run()

			break
		}
	}
})()
